# ────────────────────────────────────────────────────────────────
# Matryo<PERSON>ka-CoT fine-tuning launcher
# ────────────────────────────────────────────────────────────────

# Model / data
MODEL_NAME="Llama-3.1-8B-Instruct"
DATA_NAME="/data_x/junkim100/projects/matryoshka_cot/data/10k_cot_collection.jsonl"
MAX_LENGTH=16384

# Run identifiers
RUN_NAME="matryoshka-openmath-Llama-3.1-8B-Instruct"
CUDA_DEVICES="0,1,2,3,4,5,6,7"

# Optimisation
LEARNING_RATE=1e-5
EPOCHS=2

# Logs
mkdir -p logs/train
LOG_FILE="logs/train/${RUN_NAME}.log"

echo "===== Matryoshka-CoT training ====="
echo "Model            : $MODEL_NAME"
echo "Data             : $DATA_NAME"
echo "Run name         : $RUN_NAME"
echo "Learning rate    : $LEARNING_RATE"
echo "Epochs           : $EPOCHS"
echo "CUDA devices     : $CUDA_DEVICES"
echo "DeepSpeed config : deepspeed3.json"
echo "Log file         : $LOG_FILE"
echo "==================================="

# Launch in background
nohup bash -c "
CUDA_VISIBLE_DEVICES=$CUDA_DEVICES \
deepspeed --master_port 49056 train_matryoshka_cot.py \
  --deepspeed deepspeed3.json \
  --proctitle junkim100 \
  --model_name_or_path $MODEL_NAME \
  --data_name '$DATA_NAME' \
  --wb_project matryoshka_cot \
  --wb_name $RUN_NAME \
  --run_name $RUN_NAME \
  --output_name $RUN_NAME \
  --max_length $MAX_LENGTH \
  --num_train_epochs $EPOCHS \
  --per_device_train_batch_size 1 \
  --per_device_eval_batch_size 1 \
  --gradient_accumulation_steps 1 \
  --save_only_model \
  --learning_rate $LEARNING_RATE \
  --weight_decay 0.0 \
  --warmup_ratio 0.0 \
  --lr_scheduler_type cosine \
  --bf16 true \
  --tf32 true \
  --gradient_checkpointing true \
  --logging_steps 1
" &> "$LOG_FILE" &

PID=$!
echo "Matryoshka-CoT training started (PID $PID)"
echo "watch -d -t 'tail -n 1 $LOG_FILE'  # to monitor progress"
echo "kill $PID             # to stop"
