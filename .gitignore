# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyTorch
*.pth
*.pt
*.bin
*.safetensors

# Jupyter Notebook
.ipynb_checkpoints
*.ipynb

# Environment variables
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Training outputs
output/
outputs/
checkpoints/
runs/
logs/
wandb/
*.log

# Data files (uncomment if you want to ignore data)
# data/
# *.jsonl
# *.json
# *.csv
# *.tsv

# Model files
models/
*.model
*.ckpt

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Cache
.cache/
*.cache

# HuggingFace
.huggingface/
transformers_cache/
