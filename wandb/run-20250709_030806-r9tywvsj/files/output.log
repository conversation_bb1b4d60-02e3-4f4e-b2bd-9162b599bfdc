  0%|                                                                                                                                                                                                                      | 0/79 [00:00<?, ?it/s]/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/nn/parallel/_functions.py:71: UserWarning: Was asked to gather along dimension 0, but all input tensors were scalars; will instead unsqueeze and return a vector.
  warnings.warn(
Traceback (most recent call last):
  File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 648, in <module>
    train(m_args, d_args, t_args)
  File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 613, in train
    trainer.train()
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2240, in train
    return inner_training_loop(
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2555, in _inner_training_loop
    tr_loss_step = self.training_step(model, inputs, num_items_in_batch)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 3791, in training_step
    self.accelerator.backward(loss, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/accelerate/accelerator.py", line 2553, in backward
    loss.backward(**kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/_tensor.py", line 648, in backward
    torch.autograd.backward(
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/autograd/__init__.py", line 353, in backward
    _engine_run_backward(
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/autograd/graph.py", line 824, in _engine_run_backward
    return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 112.00 MiB. GPU 0 has a total capacity of 47.54 GiB of which 71.69 MiB is free. Process 1490434 has 4.09 GiB memory in use. Including non-PyTorch memory, this process has 43.36 GiB memory in use. Of the allocated memory 42.53 GiB is allocated by PyTorch, and 429.81 MiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
