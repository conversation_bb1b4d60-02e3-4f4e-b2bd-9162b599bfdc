  0%|                                                                                                                                                                                                                       | 0/3 [00:00<?, ?it/s]Trainer.tokenizer is now deprecated. You should use Trainer.processing_class instead.
Traceback (most recent call last):
  File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 318, in <module>
    train(m_args, d_args, t_args)
  File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 292, in train
    trainer.train()
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2240, in train
    return inner_training_loop(
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2555, in _inner_training_loop
    tr_loss_step = self.training_step(model, inputs, num_items_in_batch)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 3745, in training_step
    loss = self.compute_loss(model, inputs, num_items_in_batch=num_items_in_batch)
  File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 224, in compute_loss
    seq = torch.cat([prompt_ids[i], tgt_ids]).to(device)
RuntimeError: Expected all tensors to be on the same device, but found at least two devices, cuda:0 and cpu! (when checking argument for argument tensors in method wrapper_CUDA_cat)
