2025-07-08 21:59:15,067 INFO    MainThread:1339859 [wandb_setup.py:_flush():80] Current SDK version is 0.21.0
2025-07-08 21:59:15,067 INFO    MainThread:1339859 [wandb_setup.py:_flush():80] Configure stats pid to 1339859
2025-07-08 21:59:15,067 INFO    MainThread:1339859 [wandb_setup.py:_flush():80] Loading settings from /mnt/raid6/junkim100/.config/wandb/settings
2025-07-08 21:59:15,067 INFO    MainThread:1339859 [wandb_setup.py:_flush():80] Loading settings from /data_x/junkim100/projects/matryoshka_cot/wandb/settings
2025-07-08 21:59:15,067 INFO    MainThread:1339859 [wandb_setup.py:_flush():80] Loading settings from environment variables
2025-07-08 21:59:15,067 INFO    MainThread:1339859 [wandb_init.py:setup_run_log_directory():703] Logging user logs to /data_x/junkim100/projects/matryoshka_cot/wandb/run-20250708_215915-z8f9haru/logs/debug.log
2025-07-08 21:59:15,067 INFO    MainThread:1339859 [wandb_init.py:setup_run_log_directory():704] Logging internal logs to /data_x/junkim100/projects/matryoshka_cot/wandb/run-20250708_215915-z8f9haru/logs/debug-internal.log
2025-07-08 21:59:15,067 INFO    MainThread:1339859 [wandb_init.py:init():830] calling init triggers
2025-07-08 21:59:15,067 INFO    MainThread:1339859 [wandb_init.py:init():835] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-07-08 21:59:15,067 INFO    MainThread:1339859 [wandb_init.py:init():871] starting backend
2025-07-08 21:59:15,273 INFO    MainThread:1339859 [wandb_init.py:init():874] sending inform_init request
2025-07-08 21:59:15,276 INFO    MainThread:1339859 [wandb_init.py:init():882] backend started and connected
2025-07-08 21:59:15,277 INFO    MainThread:1339859 [wandb_init.py:init():953] updated telemetry
2025-07-08 21:59:15,278 INFO    MainThread:1339859 [wandb_init.py:init():977] communicating run to backend with 90.0 second timeout
