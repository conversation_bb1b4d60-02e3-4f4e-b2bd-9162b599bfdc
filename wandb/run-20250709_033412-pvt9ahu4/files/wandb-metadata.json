{"os": "Linux-6.5.0-34-generic-x86_64-with-glibc2.35", "python": "CPython 3.10.0", "startedAt": "2025-07-08T18:34:12.479776Z", "args": ["--model_name_or_path", "meta-llama/Llama-3.1-8B-Instruct", "--data_name", "data/10k_open_math_reasoning.jsonl", "--output_dir", "./output/Llama-3.1-8B-Instruct/open_math_reasoning", "--num_depths", "8", "--per_device_train_batch_size", "1", "--learning_rate", "5e-6", "--num_train_epochs", "2", "--save_steps", "500", "--logging_steps", "100", "--warmup_steps", "100", "--weight_decay", "0.01", "--bf16", "--dataloader_drop_last", "--remove_unused_columns", "false", "--log_level", "warning", "--run_name", "Llama-3.1-8B-Instruct-10k_open_math_reasoning"], "program": "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", "codePath": "train_matryoshka_cot.py", "codePathLocal": "train_matryoshka_cot.py", "email": "<EMAIL>", "root": "/data_x/junkim100/projects/matryoshka_cot", "host": "nlp-server-16", "executable": "/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python", "cpu_count": 64, "cpu_count_logical": 128, "gpu": "NVIDIA RTX A6000", "gpu_count": 8, "disk": {"/": {"total": "1964618686464", "used": "191219892224"}}, "memory": {"total": "2151664984064"}, "gpu_nvidia": [{"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-855eb063-38d7-c437-10c6-9868f029f701"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-d6dc840b-cb82-39f6-5ea2-b0a4f18bdf6e"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-47d47859-f7c9-3a5d-041f-91caee50ac0c"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-17d9dba6-e295-fcfa-0378-5fb370c94055"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-13aaa27d-9c08-63b9-a5f4-3459c3f0ffbe"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-8c83a879-8638-6ecc-eac2-281d5b59d505"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-3f97dcea-347c-cd62-b3b8-52705086bf09"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-e37570d8-ee58-0645-2da3-3234a6b9ef3f"}], "cudaVersion": "12.3", "writerId": "ebzhyuk1fiqw3tdqp92v60wd8088i5vu"}