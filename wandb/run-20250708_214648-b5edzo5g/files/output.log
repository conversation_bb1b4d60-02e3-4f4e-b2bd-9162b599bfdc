  0%|                                                                                                                                                                                                                       | 0/5 [00:00<?, ?it/s]2025-07-08 21:46:49 | WARNING | DataCollator received batch of size: 8
2025-07-08 21:46:49 | WARNING | Batch item 0 type: <class 'dict'>
2025-07-08 21:46:49 | WARNING | Batch item 0 keys: ['prompt_ids', 'solution_txt']
2025-07-08 21:46:49 | WARNING | Batch item 1 type: <class 'dict'>
2025-07-08 21:46:49 | WARNING | Batch item 1 keys: ['prompt_ids', 'solution_txt']
2025-07-08 21:46:49 | WARNING | Batch item 2 type: <class 'dict'>
2025-07-08 21:46:49 | WARNING | Batch item 2 keys: ['prompt_ids', 'solution_txt']
2025-07-08 21:46:49 | WARNING | DataCollator received batch of size: 8
2025-07-08 21:46:49 | WARNING | Batch item 0 type: <class 'dict'>
2025-07-08 21:46:49 | WARNING | Batch item 0 keys: ['prompt_ids', 'solution_txt']
2025-07-08 21:46:49 | WARNING | Batch item 1 type: <class 'dict'>
2025-07-08 21:46:49 | WARNING | Batch item 1 keys: ['prompt_ids', 'solution_txt']
2025-07-08 21:46:49 | WARNING | Batch item 2 type: <class 'dict'>
2025-07-08 21:46:49 | WARNING | Batch item 2 keys: ['prompt_ids', 'solution_txt']
Traceback (most recent call last):
  File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 340, in <module>
    train(m_args, d_args, t_args)
  File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 314, in train
    trainer.train()
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2240, in train
    return inner_training_loop(
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2555, in _inner_training_loop
    tr_loss_step = self.training_step(model, inputs, num_items_in_batch)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 3745, in training_step
    loss = self.compute_loss(model, inputs, num_items_in_batch=num_items_in_batch)
TypeError: MatryoshkaTrainer.compute_loss() got an unexpected keyword argument 'num_items_in_batch'
