{"time":"2025-07-08T21:44:54.09762801+09:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-07-08T21:44:55.554638713+09:00","level":"INFO","msg":"stream: created new stream","id":"zk7wowgt"}
{"time":"2025-07-08T21:44:55.55473217+09:00","level":"INFO","msg":"stream: started","id":"zk7wowgt"}
{"time":"2025-07-08T21:44:55.554758059+09:00","level":"INFO","msg":"writer: Do: started","stream_id":"zk7wowgt"}
{"time":"2025-07-08T21:44:55.554840214+09:00","level":"INFO","msg":"sender: started","stream_id":"zk7wowgt"}
{"time":"2025-07-08T21:44:55.554781944+09:00","level":"INFO","msg":"handler: started","stream_id":"zk7wowgt"}
{"time":"2025-07-08T21:44:56.870047756+09:00","level":"INFO","msg":"stream: closing","id":"zk7wowgt"}
{"time":"2025-07-08T21:44:57.*********+09:00","level":"ERROR","msg":"file transfer: upload: failed to upload: 400 Bad Request","task":"DefaultUploadTask{FileKind: 1, Path: /data_x/junkim100/projects/matryoshka_cot/wandb/run-20250708_214453-zk7wowgt/files/wandb-metadata.json, Name: wandb-metadata.json, Url: https://storage.googleapis.com/wandb-production.appspot.com/junkim/test/zk7wowgt/wandb-metadata.json?X-Goog-Algorithm=GOOG4-RSA-SHA256&X-Goog-Credential=gorilla-files-url-signer-man%40wandb-production.iam.gserviceaccount.com%2F20250708%2Fauto%2Fstorage%2Fgoog4_request&X-Goog-Date=20250708T124456Z&X-Goog-Expires=86399&X-Goog-Signature=2307d558f431b98382d5e78eed40164be7def36439c0c71770a25b769ecb3830d40a2e29b104a5243caae36f8f3e97fc86feb8cb4196f6a7c9ec38789e7918c7545f4cd1e79bd422482459b9491f4683666fc0ed9d56d085ca36f7ecd27ebe417856bab86a5ec43156d93edbdb80d98111a03cbb2d23fa15680d828e50010f11b08b164ff38caa92333bf13a90fb3e0015a27c20f50c585aab001d3df98808564fef1b92e56dcbe07c0564962a5c252d670969bdaeb30b5722ab19a04330cf6a87f2e22f27a96706b699086a93fd107c5907fb7449f78d75da32d87dfee61b7e11ae27329de1d5278e59b029f5c8dc951a705fe683f8450a71116ac470cb2876&X-Goog-SignedHeaders=host&X-User=junkim100, Size: 1047}"}
