  0%|                                                                                                                                                                                                                    | 0/1250 [00:00<?, ?it/s]2025-07-08 21:44:56 | WARNING | DataCollator received batch of size: 8
2025-07-08 21:44:56 | WARNING | Batch item 0 type: <class 'dict'>
2025-07-08 21:44:56 | WARNING | Batch item 0 keys: []
2025-07-08 21:44:56 | WARNING | Batch item 1 type: <class 'dict'>
2025-07-08 21:44:56 | WARNING | Batch item 1 keys: []
2025-07-08 21:44:56 | WARNING | Batch item 2 type: <class 'dict'>
2025-07-08 21:44:56 | WARNING | Batch item 2 keys: []
2025-07-08 21:44:56 | ERROR | KeyError accessing 'prompt_ids': 'prompt_ids'
2025-07-08 21:44:56 | ERROR | Available keys in first batch item: []
Traceback (most recent call last):
  File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 337, in <module>
    train(m_args, d_args, t_args)
  File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 311, in train
    trainer.train()
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2240, in train
    return inner_training_loop(
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2509, in _inner_training_loop
    batch_samples, num_items_in_batch = self.get_batch_samples(epoch_iterator, num_batches, args.device)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 5263, in get_batch_samples
    batch_samples.append(next(epoch_iterator))
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/accelerate/data_loader.py", line 567, in __iter__
    current_batch = next(dataloader_iter)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 733, in __next__
    data = self._next_data()
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 789, in _next_data
    data = self._dataset_fetcher.fetch(index)  # may raise StopIteration
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/_utils/fetch.py", line 55, in fetch
    return self.collate_fn(data)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer_utils.py", line 872, in __call__
    return self.data_collator(features)
  File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 198, in __call__
    prompt_ids = [b["prompt_ids"] for b in batch]
  File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 198, in <listcomp>
    prompt_ids = [b["prompt_ids"] for b in batch]
KeyError: 'prompt_ids'
