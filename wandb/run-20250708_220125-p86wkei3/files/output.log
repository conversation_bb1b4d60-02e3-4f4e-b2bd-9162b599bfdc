  0%|                                                                                                                                                                                                                       | 0/1 [00:00<?, ?it/s]Trainer.tokenizer is now deprecated. You should use Trainer.processing_class instead.
Trainer.tokenizer is now deprecated. You should use Trainer.processing_class instead.
Trainer.tokenizer is now deprecated. You should use Trainer.processing_class instead.
Trainer.tokenizer is now deprecated. You should use Trainer.processing_class instead.
Trainer.tokenizer is now deprecated. You should use Trainer.processing_class instead.
Trainer.tokenizer is now deprecated. You should use Trainer.processing_class instead.
Trainer.tokenizer is now deprecated. You should use Trainer.processing_class instead.
Trainer.tokenizer is now deprecated. You should use Trainer.processing_class instead.
Trainer.tokenizer is now deprecated. You should use Trainer.processing_class instead.
Trainer.tokenizer is now deprecated. You should use Trainer.processing_class instead.
Traceback (most recent call last):
  File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 335, in <module>
    train(m_args, d_args, t_args)
  File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 300, in train
    trainer.train()
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2240, in train
    return inner_training_loop(
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2555, in _inner_training_loop
    tr_loss_step = self.training_step(model, inputs, num_items_in_batch)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 3745, in training_step
    loss = self.compute_loss(model, inputs, num_items_in_batch=num_items_in_batch)
  File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 246, in compute_loss
    out = model(input_ids=seqs, attention_mask=attn, labels=labels)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1751, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/nn/modules/module.py", line 1762, in _call_impl
    return forward_call(*args, **kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/nn/parallel/data_parallel.py", line 194, in forward
    outputs = self.parallel_apply(replicas, inputs, module_kwargs)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/nn/parallel/data_parallel.py", line 213, in parallel_apply
    return parallel_apply(
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/nn/parallel/parallel_apply.py", line 119, in parallel_apply
    thread.join()
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/threading.py", line 1089, in join
    self._wait_for_tstate_lock()
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/threading.py", line 1105, in _wait_for_tstate_lock
    elif lock.acquire(block, timeout):
KeyboardInterrupt
