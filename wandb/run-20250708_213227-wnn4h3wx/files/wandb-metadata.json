{"os": "Linux-6.5.0-34-generic-x86_64-with-glibc2.35", "python": "CPython 3.10.0", "startedAt": "2025-07-08T12:32:27.012039Z", "args": ["--local_rank=0", "--deep<PERSON><PERSON>", "deepspeed3.json", "--proctitle", "junkim100", "--model_name_or_path", "Llama-3.1-8B-Instruct", "--data_name", "/data_x/junkim100/projects/matryoshka_cot/data/10k_cot_collection.jsonl", "--wb_project", "matryoshka_cot", "--wb_name", "matryoshka-openmath-Llama-3.1-8B-Instruct", "--output_name", "matryoshka-openmath-Llama-3.1-8B-Instruct", "--output_dir", "./output", "--max_length", "16384", "--num_train_epochs", "2", "--per_device_train_batch_size", "1", "--per_device_eval_batch_size", "1", "--gradient_accumulation_steps", "1", "--save_only_model", "--learning_rate", "1e-5", "--weight_decay", "0.0", "--warmup_ratio", "0.0", "--lr_scheduler_type", "cosine", "--bf16", "true", "--tf32", "true", "--gradient_checkpointing", "true", "--logging_steps", "1"], "program": "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", "codePath": "train_matryoshka_cot.py", "codePathLocal": "train_matryoshka_cot.py", "email": "<EMAIL>", "root": "/data_x/junkim100/projects/matryoshka_cot", "host": "nlp-server-16", "executable": "/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10", "cpu_count": 64, "cpu_count_logical": 128, "gpu": "NVIDIA RTX A6000", "gpu_count": 8, "disk": {"/": {"total": "1964618686464", "used": "191210373120"}}, "memory": {"total": "2151664984064"}, "gpu_nvidia": [{"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-855eb063-38d7-c437-10c6-9868f029f701"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-d6dc840b-cb82-39f6-5ea2-b0a4f18bdf6e"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-47d47859-f7c9-3a5d-041f-91caee50ac0c"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-17d9dba6-e295-fcfa-0378-5fb370c94055"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-13aaa27d-9c08-63b9-a5f4-3459c3f0ffbe"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-8c83a879-8638-6ecc-eac2-281d5b59d505"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-3f97dcea-347c-cd62-b3b8-52705086bf09"}, {"name": "NVIDIA RTX A6000", "memoryTotal": "51527024640", "cudaCores": 10752, "architecture": "Ampere", "uuid": "GPU-e37570d8-ee58-0645-2da3-3234a6b9ef3f"}], "cudaVersion": "12.3", "writerId": "1tv6yruwzwj8llp6h7hgqrwonrpztj5n"}