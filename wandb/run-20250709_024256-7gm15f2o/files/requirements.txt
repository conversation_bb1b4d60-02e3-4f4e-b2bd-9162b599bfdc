word2number==1.1
sqlitedict==2.1.0
tcolorpy==0.1.6
tabulate==0.9.0
pybind11==2.13.6
portalocker==2.10.1
pathvalidate==3.2.1
mbstrdecoder==1.1.3
jsonlines==4.0.0
rsa==4.9
absl-py==2.1.0
typepy==1.3.2
tqdm-multiprocess==0.0.11
sacrebleu==2.4.3
numexpr==2.10.1
rouge_score==0.1.2
DataProperty==1.0.1
tabledata==1.3.3
pytablewriter==1.2.0
evaluate==0.4.3
py-cpuinfo==9.0.0
ninja==********
hjson==3.1.0
msgpack==1.1.0
py==1.11.0
decorator==5.1.1
retry==0.9.2
requests-oauthlib==2.0.0
google-auth==2.36.0
google-auth-oauthlib==1.2.1
gspread==6.1.4
oauth2client==4.1.3
einops==0.8.0
sentencepiece==0.2.0
tensorboard-data-server==0.7.2
pyarrow-hotfix==0.6
Markdown==3.7
grpcio==1.68.1
docker-pycreds==0.4.0
Werkzeug==3.1.3
tensorboardX==*******
nltk==3.8.1
tensorboard==2.18.0
py-spy==0.4.0
opencensus-context==0.1.3
nvidia-ml-py==12.570.86
distlib==0.3.9
colorful==0.5.6
blake3==1.0.4
wrapt==1.17.2
websockets==14.2
virtualenv==20.29.2
uvloop==0.21.0
opencensus==0.11.4
pycountry==24.6.1
proto-plus==1.26.0
partial-json-parser==*******.post5
msgspec==0.19.0
lark==1.2.2
interegular==0.3.3
httptools==0.6.4
googleapis-common-protos==1.67.0
diskcache==5.6.3
cloudpickle==3.1.1
astor==0.8.1
airportsdata==20241001
uvicorn==0.34.0
tiktoken==0.9.0
smart-open==7.1.0
opencv-python-headless==*********
depyf==0.18.0
watchfiles==1.0.4
starlette==0.45.3
google-api-core==2.24.1
prometheus-fastapi-instrumentator==7.0.2
fastapi==0.115.8
aiohttp-cors==0.7.0
outlines_core==0.1.26
setuptools==78.1.1
wheel==0.45.1
pip==25.1
nvidia-cusparselt-cu12==0.6.3
typing_extensions==4.12.2
accelerate==1.8.1
nvidia-nvtx-cu12==12.6.77
nvidia-nvjitlink-cu12==12.6.85
nvidia-curand-cu12==*********
torch==2.7.1
nvidia-cufile-cu12==********
nvidia-cuda-runtime-cu12==12.6.77
nvidia-cuda-nvrtc-cu12==12.6.77
nvidia-cuda-cupti-cu12==12.6.80
nvidia-cublas-cu12==********
networkx==3.3
MarkupSafe==2.1.5
fsspec==2024.6.1
filelock==3.13.1
nvidia-cusparse-cu12==********
nvidia-cufft-cu12==********
nvidia-cudnn-cu12==********
Jinja2==3.1.4
nvidia-cusolver-cu12==********
triton==3.3.1
nvidia-nccl-cu12==2.26.2
sympy==1.14.0
torchvision==0.22.1
urllib3==2.5.0
typing-inspection==0.4.1
tqdm==4.67.1
safetensors==0.5.3
regex==2024.11.6
PyYAML==6.0.2
pydantic_core==2.33.2
psutil==7.0.0
packaging==25.0
certifi==2025.6.15
annotated-types==0.7.0
numpy==2.1.2
idna==3.10
hf-xet==1.1.5
charset-normalizer==3.4.2
requests==2.32.4
pydantic==2.11.7
huggingface-hub==0.33.2
tokenizers==0.21.2
deepspeed==0.16.9
transformers==4.52.3
setproctitle==1.3.6
pytz==2025.2
xxhash==3.5.0
tzdata==2025.2
six==1.17.0
pyarrow==20.0.0
propcache==0.3.2
multidict==6.6.3
frozenlist==1.7.0
dill==0.3.8
attrs==25.3.0
async-timeout==5.0.1
aiohappyeyeballs==2.6.1
yarl==1.20.1
python-dateutil==2.9.0.post0
multiprocess==0.70.16
aiosignal==1.3.2
pandas==2.3.0
aiohttp==3.12.13
datasets==3.6.0
mpmath==1.3.0
pillow==11.0.0
torchaudio==2.7.1
termcolor==3.1.0
fire==0.7.0
smmap==5.0.2
sentry-sdk==2.32.0
protobuf==6.31.1
platformdirs==4.3.8
click==8.2.1
gitdb==4.0.12
GitPython==3.1.44
wandb==0.21.0
autocommand==2.2.2
backports.tarfile==1.2.0
importlib_metadata==8.0.0
inflect==7.3.1
jaraco.collections==5.1.0
jaraco.context==5.3.0
jaraco.functools==4.0.1
jaraco.text==3.12.1
more-itertools==10.3.0
packaging==24.2
platformdirs==4.2.2
tomli==2.0.1
typeguard==4.3.0
typing_extensions==4.12.2
wheel==0.45.1
zipp==3.19.2
