# Matryoshka Chain-of-Thought (Matryoshka-CoT)

🎯 **Adaptive Reasoning Depth Training for Efficient Language Models**

## 🚀 Overview

Matryoshka-CoT is a novel training methodology that enables language models to dynamically adjust their reasoning depth based on problem complexity. Instead of using the same lengthy reasoning process for all questions, models learn to:

- **Answer quickly** for simple problems (shallow reasoning)
- **Think deeply** for complex problems (full chain-of-thought)

This solves the efficiency vs. capability trade-off in modern thinking models like OpenAI's o1.

## 🎭 The Problem

Current thinking models are slow for simple questions because they apply the same deep reasoning process regardless of problem complexity. A model shouldn't need 30 seconds of reasoning to answer "What is 2+2?"

## 💡 The Solution

**Matryoshka-CoT** trains models with multiple reasoning depths simultaneously:

```
Depth 0: "answer: 4"                           # Quick answer
Depth 1: "2+2 is basic addition\n\nanswer: 4" # Light reasoning  
Depth 2: "Let me solve this step by step..."   # Full reasoning
```

### 🔑 Key Innovation

The training methodology processes **all depths in a single forward pass** before backpropagation, ensuring the model sees complete data patterns across all reasoning levels without cutoff.

## 🏗️ Architecture

### Training Process
1. **Multi-depth Loss**: Compute loss for all reasoning depths (0, 192, 384, 576, 768, 960, 1152, 1344 tokens)
2. **Weighted Combination**: Combine losses with equal weights across depths
3. **Single Backprop**: Update weights only after seeing all depths

### Depth Calculation
- **95th Percentile Approach**: Uses 95th percentile of reasoning lengths to avoid outlier dominance
- **Automatic Outlier Detection**: Warns when extreme outliers would skew depth calculations
- **Token-based Depths**: Evenly distributed reasoning token limits

## 🚀 Quick Start

### Installation

```bash
git clone https://github.com/junkim100/Matryoshka-CoT.git
cd Matryoshka-CoT
pip install -r requirements.txt  # Create this based on your dependencies
```

### Training

```bash
# Basic training with default settings
bash run_matryoshka_cot.sh

# Custom configuration
bash run_matryoshka_cot.sh \
  --batch_size 1 \
  --learning_rate 1e-5 \
  --num_epochs 2 \
  --max_length 2048 \
  --custom_depths "0,192,384,768"
```

### Demo & Validation

```bash
# Test depth logic with sample data
python depth_demo.py --file data/your_dataset.jsonl --num_depths 8

# Analyze dataset token distributions
jupyter notebook depth_analysis.ipynb
```

## 📊 Features

- ✅ **95th Percentile Depth Calculation**: Prevents outlier dominance
- ✅ **Automatic Outlier Detection**: Intelligent depth adjustment
- ✅ **Clean Logging**: Minimal output with essential information
- ✅ **WandB Integration**: Training monitoring and visualization
- ✅ **Flexible Configuration**: Configurable via shell script parameters
- ✅ **Depth Validation**: Ensures training logic correctness

## 🎯 Configuration Options

| Parameter | Default | Description |
|-----------|---------|-------------|
| `--batch_size` | 1 | Training batch size |
| `--learning_rate` | 5e-6 | Learning rate |
| `--num_depths` | 8 | Number of reasoning depths |
| `--max_length` | 4096 | Maximum sequence length |
| `--custom_depths` | Auto | Custom depth values (e.g., "0,2,4,8") |
| `--use_sentence_depths` | false | Use sentence-based vs token-based depths |

## 📈 Results

The methodology enables models to:
- **Maintain reasoning quality** on complex problems
- **Improve response speed** on simple problems  
- **Learn adaptive reasoning** patterns automatically

## 🔬 Technical Details

### Depth Calculation Algorithm
1. Analyze reasoning token lengths across dataset
2. Calculate 95th percentile to avoid outliers
3. Distribute depths evenly from 0 to 95th percentile
4. Validate depth containment property

### Loss Computation
```python
total_loss = 0.0
for depth, weight in zip(depths, weights):
    # Process batch at this depth
    depth_loss = compute_depth_loss(batch, depth)
    total_loss += weight * depth_loss

# Single backpropagation after all depths
total_loss.backward()
```

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Inspired by Matryoshka Representation Learning
- Built on HuggingFace Transformers
- Uses WandB for experiment tracking

---

**🎭 "Like Russian nesting dolls, but for AI reasoning"**
