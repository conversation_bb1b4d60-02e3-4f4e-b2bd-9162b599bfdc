import json, argparse, random, pathlib, logging
from typing import List, Tuple, Optional
import transformers
import torch

IGNORE_INDEX = -100


# ------------------------------------------------------------------ #
#  Helper functions from training script
def find_answer_start(
    reasoning_ids: torch.Tensor, boxed_token_id: int, answer_fallback: int = 6
) -> int:
    """Find the start of the answer section (\\boxed{...})."""
    try:
        boxed_indices = (reasoning_ids == boxed_token_id).nonzero(as_tuple=True)[0]
        if len(boxed_indices) > 0:
            return boxed_indices[0].item()
    except Exception:
        pass
    # Fallback: assume last N tokens are the answer
    return max(0, len(reasoning_ids) - answer_fallback)


def token_prefixes(
    reasoning_ids: torch.Tensor,
    depths: tuple[int, ...],
    boxed_token_id: int,
    answer_fallback: int = 6,
    tokenizer: transformers.PreTrainedTokenizer = None,
) -> dict[int, torch.Tensor]:
    """
    Build Matryoshka prefixes keyed by `depth`.  Depth 0 = answer only;
    depth d>0 = first `d` reasoning tokens + "\n\nanswer: " + full answer.
    """
    ans_start = find_answer_start(reasoning_ids, boxed_token_id, answer_fallback)
    answer_ids = reasoning_ids[ans_start:]  # includes \boxed … period
    think_ids = reasoning_ids[:ans_start]  # pure reasoning

    # Encode the answer separator
    if tokenizer is not None:
        answer_sep_ids = tokenizer.encode("\n\nanswer: ", add_special_tokens=False)
        answer_sep_tensor = torch.tensor(answer_sep_ids, dtype=reasoning_ids.dtype, device=reasoning_ids.device)
    else:
        # Fallback if no tokenizer provided
        answer_sep_tensor = torch.tensor([], dtype=reasoning_ids.dtype, device=reasoning_ids.device)

    out = {}
    for d in depths:
        if d == 0:
            # For depth 0, just "answer: " + answer
            if tokenizer is not None:
                answer_only_sep = tokenizer.encode("answer: ", add_special_tokens=False)
                answer_only_sep_tensor = torch.tensor(answer_only_sep, dtype=reasoning_ids.dtype, device=reasoning_ids.device)
                out[d] = torch.cat([answer_only_sep_tensor, answer_ids])
            else:
                out[d] = answer_ids
        else:
            # For depth > 0, reasoning + "\n\nanswer: " + answer
            out[d] = torch.cat([think_ids[:d], answer_sep_tensor, answer_ids])
    return out


def calculate_depths_and_weights(
    num_depths: int = 4,
    use_sentence_depths: bool = True,
    custom_depths: Optional[str] = None,
    dataset_solutions: List[str] = None,
    tokenizer: transformers.PreTrainedTokenizer = None,
) -> Tuple[Tuple[int, ...], Tuple[float, ...]]:
    """
    Calculate depths and weights based on configuration.
    """
    # Handle custom depths first
    if custom_depths:
        try:
            depths = tuple(int(d.strip()) for d in custom_depths.split(","))
            weights = tuple(1.0 for _ in depths)
            print(f"Using custom depths: {depths}")
            return depths, weights
        except ValueError as e:
            print(
                f"Invalid custom_depths format '{custom_depths}': {e}. Falling back to default."
            )

    # Token-based depths
    if not use_sentence_depths:
        if dataset_solutions is None or tokenizer is None:
            print(
                "No dataset solutions or tokenizer provided for token-based depths. Using sentence-based fallback."
            )
            return calculate_depths_and_weights(num_depths, True, None, None, None)

        # Analyze token lengths of reasoning parts
        reasoning_lengths = []
        boxed_token_id = (
            tokenizer.encode("\\boxed", add_special_tokens=False)[0]
            if tokenizer.encode("\\boxed", add_special_tokens=False)
            else None
        )

        for sol in dataset_solutions[:1000]:  # Sample first 1000 for efficiency
            try:
                sol_ids = tokenizer(
                    sol, add_special_tokens=False, return_tensors="pt"
                ).input_ids[0]
                if boxed_token_id is not None:
                    ans_start = find_answer_start(sol_ids, boxed_token_id, 6)
                    reasoning_length = ans_start
                else:
                    # Fallback: assume last 20% is answer
                    reasoning_length = int(len(sol_ids) * 0.8)
                reasoning_lengths.append(reasoning_length)
            except Exception:
                continue

        if not reasoning_lengths:
            print("No valid reasoning lengths found. Using sentence-based fallback.")
            return calculate_depths_and_weights(num_depths, True, None, None, None)

        max_reasoning_length = max(reasoning_lengths)
        avg_reasoning_length = sum(reasoning_lengths) / len(reasoning_lengths)

        print(
            f"Reasoning token analysis: max_length={max_reasoning_length}, avg_length={avg_reasoning_length:.1f}"
        )

        # Generate token-based depths
        if num_depths == 1:
            depths = (0,)
        else:
            # Always include 0 (answer-only), then divide reasoning tokens evenly
            step = (
                max_reasoning_length // (num_depths - 1)
                if num_depths > 1
                else max_reasoning_length
            )
            depths = tuple(
                [0]
                + [min(step * i, max_reasoning_length) for i in range(1, num_depths)]
            )

        weights = tuple(1.0 for _ in depths)
        print(f"Using token-based depths: {depths}")
        return depths, weights

    # Sentence-based depths (original behavior)
    if num_depths == 4:
        depths = (0, 2, 4, 8)  # Original default
    else:
        # Generate sentence-based depths: 0, then evenly spaced up to 2*num_depths
        max_sentences = 2 * num_depths
        if num_depths == 1:
            depths = (0,)
        else:
            step = max_sentences // (num_depths - 1)
            depths = tuple([0] + [step * i for i in range(1, num_depths)])

    weights = tuple(1.0 for _ in depths)
    print(f"Using sentence-based depths: {depths}")
    return depths, weights


# ------------------------------------------------------------------ #
#  DemoTrainer.compute_loss  (printing only, no back-prop)
class DemoTrainer:
    def __init__(self, tokenizer, depths, weights):
        self.tok = tokenizer
        self.depths = depths
        self.weights = weights
        # Get boxed token ID for proper answer detection
        self.boxed_token_id = (
            tokenizer.encode("\\boxed", add_special_tokens=False)[0]
            if tokenizer.encode("\\boxed", add_special_tokens=False)
            else None
        )

    def compute_loss(self, prompts, solutions):
        print(f"\n🎯 MATRYOSHKA DEPTH DEMO")
        print(f"📊 Depths: {self.depths}")
        print(f"⚖️  Weights: {self.weights}")

        # Store all assistant texts for validation
        all_assistant_texts = {}  # {sample_idx: {depth: text}}

        for depth, w in zip(self.depths, self.weights):
            print(f"\n{'='*80}")
            print(f"🔢 DEPTH {depth} (weight {w:.1f})")
            print(f"{'='*80}")

            for idx, (p_txt, s_txt) in enumerate(zip(prompts, solutions)):
                p_ids = self.tok(p_txt, add_special_tokens=True).input_ids
                s_ids = self.tok(s_txt, add_special_tokens=False).input_ids

                # Use the same token_prefixes function as training
                if self.boxed_token_id is not None:
                    prefix = token_prefixes(
                        torch.tensor(s_ids), (depth,), self.boxed_token_id, tokenizer=self.tok
                    )[depth].tolist()
                else:
                    # Fallback to simple approach
                    answer_len = 8
                    answer_ids = s_ids[-answer_len:]
                    think_ids = s_ids[:-answer_len]
                    if depth == 0:
                        prefix = answer_ids
                    else:
                        prefix = think_ids[:depth] + answer_ids

                assistant_text = self.tok.decode(prefix, skip_special_tokens=True)

                # Store for validation
                if idx not in all_assistant_texts:
                    all_assistant_texts[idx] = {}
                all_assistant_texts[idx][depth] = assistant_text

                print(f"\n📝 Sample {idx + 1}")
                print(f"🔤 Prompt:")
                print(f"{self.tok.decode(p_ids, skip_special_tokens=True)}")
                print(f"\n🤖 Assistant (depth {depth}):")
                print(f"{assistant_text}")
                print(f"\n📏 Stats: {len(prefix)} tokens total")

                if idx >= 1:  # Only show first 2 samples per depth
                    break

        # Validation: Check containment property
        self._validate_depth_containment(all_assistant_texts)

    def _validate_depth_containment(self, all_assistant_texts):
        """Validate that smaller depth texts are contained in larger depth texts."""
        print(f"\n{'='*80}")
        print(f"🔍 VALIDATION: Checking depth containment property")
        print(f"{'='*80}")

        all_passed = True
        total_checks = 0
        passed_checks = 0

        for sample_idx, depth_texts in all_assistant_texts.items():
            print(f"\n� Sample {sample_idx + 1} validation:")

            sorted_depths = sorted(depth_texts.keys())
            sample_passed = True

            for i in range(len(sorted_depths) - 1):
                smaller_depth = sorted_depths[i]
                larger_depth = sorted_depths[i + 1]

                smaller_text = depth_texts[smaller_depth].strip()
                larger_text = depth_texts[larger_depth].strip()

                total_checks += 1

                # Check if smaller text is contained in larger text OR if they're the same
                # (same is valid when depth exceeds available reasoning tokens)
                if smaller_text in larger_text or smaller_text == larger_text:
                    if smaller_text == larger_text:
                        status = "✅ PASS (same - depth exceeds reasoning)"
                    else:
                        status = "✅ PASS (contained)"
                    passed_checks += 1
                else:
                    status = "❌ FAIL"
                    sample_passed = False
                    all_passed = False

                print(f"   Depth {smaller_depth} → {larger_depth}: {status}")

                if not (smaller_text in larger_text or smaller_text == larger_text):
                    print(f"      Smaller: '{smaller_text[:50]}...'")
                    print(f"      Larger:  '{larger_text[:50]}...'")

            if sample_passed:
                print(f"   Sample {sample_idx + 1}: ✅ ALL CHECKS PASSED")
            else:
                print(f"   Sample {sample_idx + 1}: ❌ SOME CHECKS FAILED")

        print(f"\n🎯 VALIDATION SUMMARY:")
        print(f"   Total checks: {total_checks}")
        print(f"   Passed: {passed_checks}")
        print(f"   Failed: {total_checks - passed_checks}")
        print(f"   Success rate: {passed_checks/total_checks*100:.1f}%")

        if all_passed:
            print(f"   🎉 ALL VALIDATION CHECKS PASSED!")
            print(f"   ✅ Depth containment property is satisfied")
        else:
            print(f"   ⚠️  SOME VALIDATION CHECKS FAILED!")
            print(f"   ❌ Depth containment property is violated")

        return all_passed


# ------------------------------------------------------------------ #
def main(
    path,
    sample=2,
    num_depths=8,
    use_sentence_depths=False,
    custom_depths=None,
    model_name="meta-llama/Llama-3.1-8B-Instruct",
):
    print(f"🚀 Loading data from: {path}")
    print(f"📊 Configuration:")
    print(f"   - num_depths: {num_depths}")
    print(f"   - use_sentence_depths: {use_sentence_depths}")
    print(f"   - custom_depths: {custom_depths}")
    print(f"   - sample size: {sample}")
    print(f"   - model: {model_name}")

    # load a couple of rows
    rows = [json.loads(l) for l in open(path, "r", encoding="utf-8")]
    rows = random.sample(rows, k=min(sample, len(rows)))
    prompts = [r["user"].strip() for r in rows]
    solutions = [r["assistant"].strip() for r in rows]

    print(f"\n🔤 Loading tokenizer...")
    tokenizer = transformers.AutoTokenizer.from_pretrained(model_name)

    print(f"\n🧮 Calculating depths and weights...")
    depths, weights = calculate_depths_and_weights(
        num_depths=num_depths,
        use_sentence_depths=use_sentence_depths,
        custom_depths=custom_depths,
        dataset_solutions=solutions,
        tokenizer=tokenizer,
    )

    print(f"\n🎭 Creating demo trainer...")
    demo = DemoTrainer(tokenizer, depths, weights)
    demo.compute_loss(prompts, solutions)


if __name__ == "__main__":
    ap = argparse.ArgumentParser(
        description="Demo Matryoshka depth splits with exact training settings"
    )
    ap.add_argument("--file", required=True, help="path to jsonl data")
    ap.add_argument("--sample", type=int, default=2, help="number of samples to show")
    ap.add_argument("--num_depths", type=int, default=8, help="number of depths to use")
    ap.add_argument(
        "--use_sentence_depths",
        action="store_true",
        help="use sentence-based depths instead of token-based",
    )
    ap.add_argument(
        "--custom_depths",
        type=str,
        help="custom depths as comma-separated values (e.g., '0,2,4,8')",
    )
    ap.add_argument(
        "--model_name",
        default="meta-llama/Llama-3.1-8B-Instruct",
        help="model name for tokenizer",
    )

    args = ap.parse_args()
    main(
        pathlib.Path(args.file),
        sample=args.sample,
        num_depths=args.num_depths,
        use_sentence_depths=args.use_sentence_depths,
        custom_depths=args.custom_depths,
        model_name=args.model_name,
    )
