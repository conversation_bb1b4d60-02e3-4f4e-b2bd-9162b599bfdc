import json, argparse, random, pathlib
import transformers
import torch

IGNORE_INDEX = -100
DEPTHS = (0, 16, 32)  # change to match main script
WEIGHTS = (1.0, 1.0, 1.0)
ANSWER_TOKENS = 8  # answer length heuristic


# ------------------------------------------------------------------ #
#  token_prefixes  (identical to training script)
def token_prefixes(reasoning_ids, depths, answer_len=ANSWER_TOKENS):
    answer_ids = reasoning_ids[-answer_len:]
    think_ids = reasoning_ids[:-answer_len]
    out = {}
    for d in depths:
        out[d] = answer_ids if d == 0 else torch.cat([think_ids[:d], answer_ids])
    return out


# ------------------------------------------------------------------ #
#  DemoTrainer.compute_loss  (printing only, no back-prop)
class DemoTrainer:
    def __init__(self, tokenizer, depths, weights):
        self.tok = tokenizer
        self.depths = depths
        self.weights = weights

    def compute_loss(self, prompts, solutions):
        for depth, w in zip(self.depths, self.weights):
            print(f"\n=== DEPTH {depth} (weight {w}) ===")
            for idx, (p_txt, s_txt) in enumerate(zip(prompts, solutions)):
                p_ids = self.tok(p_txt, add_special_tokens=True).input_ids
                s_ids = self.tok(s_txt, add_special_tokens=False).input_ids
                prefix = token_prefixes(torch.tensor(s_ids), (depth,))[depth].tolist()

                # full training sequence prompt ⊕ prefix
                seq_ids = p_ids + prefix
                lbl_ids = [IGNORE_INDEX] * len(p_ids) + prefix

                print(f"\nSample {idx}")
                print("- prompt :", self.tok.decode(p_ids, skip_special_tokens=True))
                print("- assist :", self.tok.decode(prefix, skip_special_tokens=True))
                print("- seq ids:", seq_ids[:40], "...")  # truncate long print
                print("- lbl ids:", lbl_ids[:40], "...")


# ------------------------------------------------------------------ #
def main(path, sample=1):
    # load a couple of rows
    rows = [json.loads(l) for l in open(path, "r", encoding="utf-8")]
    rows = random.sample(rows, k=min(sample, len(rows)))
    prompts = [r["user"].strip() for r in rows]
    solutions = [r["assistant"].strip() for r in rows]

    tokenizer = transformers.AutoTokenizer.from_pretrained(
        "meta-llama/Llama-3.1-8B-Instruct"
    )
    demo = DemoTrainer(tokenizer, DEPTHS, WEIGHTS)
    demo.compute_loss(prompts, solutions)


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--file", required=True, help="path to jsonl data")
    args = ap.parse_args()
    main(pathlib.Path(args.file))
