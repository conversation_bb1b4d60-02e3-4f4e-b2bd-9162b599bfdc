{"cells": [{"cell_type": "code", "execution_count": null, "id": "b74af566", "metadata": {}, "outputs": [], "source": ["# token_prefixes.py (or inline above the trainer)\n", "def token_prefixes(reasoning_ids, depths, answer_len=8):\n", "    \"\"\"\n", "    Build Matryoshka prefixes by *token length* instead of sentence count.\n", "\n", "    reasoning_ids : 1-D tensor of all tokens in the assistant solution\n", "    depths        : iterable of integers (e.g. 0, 16, 32…)\n", "    answer_len    : number of tokens at the tail that constitute the answer\n", "    returns       : dict {depth: tensor_of_tokens}\n", "    \"\"\"\n", "    answer_ids = reasoning_ids[-answer_len:]      # keep answer intact\n", "    think_ids  = reasoning_ids[:-answer_len]      # pure reasoning\n", "    out = {}\n", "    for d in depths:\n", "        if d == 0:\n", "            out[d] = answer_ids\n", "        else:\n", "            out[d] = torch.cat([think_ids[:d], answer_ids])\n", "    return out\n", "\n", "def compute_loss(self, model, inputs, return_outputs=False, num_items_in_batch=None):\n", "    \"\"\"\n", "    Matryoshka-CoT loss with length-based prefixes.\n", "    Depths are read from self.depths (e.g. [0,16,32,64]).\n", "    \"\"\"\n", "    prompt_ids     = inputs[\"prompt_ids\"]          # (B, P)\n", "    solutions      = inputs[\"solution_txt\"]        # list[str]\n", "    device         = prompt_ids.device\n", "    B              = prompt_ids.size(0)\n", "\n", "    total_loss = 0.0\n", "    for depth, w in zip(self.depths, self.weights.to(device)):\n", "        seqs, labels = [], []\n", "\n", "        for i in range(B):\n", "            # 1) Tokenise the full assistant solution once\n", "            full_ids = self.tokenizer(\n", "                solutions[i],\n", "                add_special_tokens=False,\n", "                return_tensors=\"pt\"\n", "            ).input_ids[0].to(device)\n", "\n", "            # 2) Slice it according to the current depth\n", "            tgt_ids = token_prefixes(full_ids, (depth,))[depth]\n", "\n", "            # 3) Build full training sequence  [prompt  ⊕  truncated-assistant]\n", "            seq = torch.cat([prompt_ids[i], tgt_ids])\n", "            lab = seq.clone()\n", "            lab[: len(prompt_ids[i])] = IGNORE_INDEX   # mask the prompt\n", "\n", "            seqs.append(seq)\n", "            labels.append(lab)\n", "\n", "        # 4) Pad to equal length inside the current depth micro-batch\n", "        seqs = torch.nn.utils.rnn.pad_sequence(\n", "            seqs, batch_first=True, padding_value=self.tokenizer.pad_token_id\n", "        )\n", "        labels = torch.nn.utils.rnn.pad_sequence(\n", "            labels, batch_first=True, padding_value=IGNORE_INDEX\n", "        )\n", "        attn = seqs.ne(self.tokenizer.pad_token_id)\n", "\n", "        # 5) Forward pass & accumulate weighted loss\n", "        outputs    = model(input_ids=seqs, attention_mask=attn, labels=labels)\n", "        total_loss = total_loss + w * outputs.loss\n", "\n", "    total_loss = total_loss / self.weights.sum()   # normalise\n", "    return (total_loss, None) if return_outputs else total_loss\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}