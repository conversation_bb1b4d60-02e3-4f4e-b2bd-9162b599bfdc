[2025-07-08 21:24:27,929] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 21:24:30,859] [WARNING] [runner.py:215:fetch_hostfile] Unable to find hostfile, will proceed with training with local resources only.
Detected VISIBLE_DEVICES=0,1,2,3,4,5,6,7: setting --include=localhost:0,1,2,3,4,5,6,7
[2025-07-08 21:24:30,859] [INFO] [runner.py:605:main] cmd = /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10 -u -m deepspeed.launcher.launch --world_info=eyJsb2NhbGhvc3QiOiBbMCwgMSwgMiwgMywgNCwgNSwgNiwgN119 --master_addr=127.0.0.1 --master_port=49056 --enable_each_rank_log=None train_matryoshka_cot.py --deepspeed deepspeed3.json --proctitle junkim100 --model_name_or_path Llama-3.1-8B-Instruct --data_name /data_x/junkim100/projects/matryoshka_cot/data/10k_cot_collection.jsonl --wb_project matryoshka_cot --wb_name matryoshka-openmath-Llama-3.1-8B-Instruct --output_name matryoshka-openmath-Llama-3.1-8B-Instruct --output_dir ./output --max_length 16384 --num_train_epochs 2 --per_device_train_batch_size 1 --per_device_eval_batch_size 1 --gradient_accumulation_steps 1 --save_only_model --learning_rate 1e-5 --weight_decay 0.0 --warmup_ratio 0.0 --lr_scheduler_type cosine --bf16 true --tf32 true --gradient_checkpointing true --logging_steps 1
[2025-07-08 21:24:32,477] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 21:24:35,310] [INFO] [launch.py:146:main] WORLD INFO DICT: {'localhost': [0, 1, 2, 3, 4, 5, 6, 7]}
[2025-07-08 21:24:35,310] [INFO] [launch.py:152:main] nnodes=1, num_local_procs=8, node_rank=0
[2025-07-08 21:24:35,310] [INFO] [launch.py:163:main] global_rank_mapping=defaultdict(<class 'list'>, {'localhost': [0, 1, 2, 3, 4, 5, 6, 7]})
[2025-07-08 21:24:35,310] [INFO] [launch.py:164:main] dist_world_size=8
[2025-07-08 21:24:35,310] [INFO] [launch.py:168:main] Setting CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
[2025-07-08 21:24:35,311] [INFO] [launch.py:256:main] process 1319787 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train_matryoshka_cot.py', '--local_rank=0', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Llama-3.1-8B-Instruct', '--data_name', '/data_x/junkim100/projects/matryoshka_cot/data/10k_cot_collection.jsonl', '--wb_project', 'matryoshka_cot', '--wb_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_dir', './output', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.0', '--warmup_ratio', '0.0', '--lr_scheduler_type', 'cosine', '--bf16', 'true', '--tf32', 'true', '--gradient_checkpointing', 'true', '--logging_steps', '1']
[2025-07-08 21:24:35,313] [INFO] [launch.py:256:main] process 1319788 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train_matryoshka_cot.py', '--local_rank=1', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Llama-3.1-8B-Instruct', '--data_name', '/data_x/junkim100/projects/matryoshka_cot/data/10k_cot_collection.jsonl', '--wb_project', 'matryoshka_cot', '--wb_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_dir', './output', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.0', '--warmup_ratio', '0.0', '--lr_scheduler_type', 'cosine', '--bf16', 'true', '--tf32', 'true', '--gradient_checkpointing', 'true', '--logging_steps', '1']
[2025-07-08 21:24:35,314] [INFO] [launch.py:256:main] process 1319789 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train_matryoshka_cot.py', '--local_rank=2', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Llama-3.1-8B-Instruct', '--data_name', '/data_x/junkim100/projects/matryoshka_cot/data/10k_cot_collection.jsonl', '--wb_project', 'matryoshka_cot', '--wb_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_dir', './output', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.0', '--warmup_ratio', '0.0', '--lr_scheduler_type', 'cosine', '--bf16', 'true', '--tf32', 'true', '--gradient_checkpointing', 'true', '--logging_steps', '1']
[2025-07-08 21:24:35,315] [INFO] [launch.py:256:main] process 1319790 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train_matryoshka_cot.py', '--local_rank=3', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Llama-3.1-8B-Instruct', '--data_name', '/data_x/junkim100/projects/matryoshka_cot/data/10k_cot_collection.jsonl', '--wb_project', 'matryoshka_cot', '--wb_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_dir', './output', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.0', '--warmup_ratio', '0.0', '--lr_scheduler_type', 'cosine', '--bf16', 'true', '--tf32', 'true', '--gradient_checkpointing', 'true', '--logging_steps', '1']
[2025-07-08 21:24:35,317] [INFO] [launch.py:256:main] process 1319791 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train_matryoshka_cot.py', '--local_rank=4', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Llama-3.1-8B-Instruct', '--data_name', '/data_x/junkim100/projects/matryoshka_cot/data/10k_cot_collection.jsonl', '--wb_project', 'matryoshka_cot', '--wb_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_dir', './output', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.0', '--warmup_ratio', '0.0', '--lr_scheduler_type', 'cosine', '--bf16', 'true', '--tf32', 'true', '--gradient_checkpointing', 'true', '--logging_steps', '1']
[2025-07-08 21:24:35,318] [INFO] [launch.py:256:main] process 1319792 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train_matryoshka_cot.py', '--local_rank=5', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Llama-3.1-8B-Instruct', '--data_name', '/data_x/junkim100/projects/matryoshka_cot/data/10k_cot_collection.jsonl', '--wb_project', 'matryoshka_cot', '--wb_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_dir', './output', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.0', '--warmup_ratio', '0.0', '--lr_scheduler_type', 'cosine', '--bf16', 'true', '--tf32', 'true', '--gradient_checkpointing', 'true', '--logging_steps', '1']
[2025-07-08 21:24:35,319] [INFO] [launch.py:256:main] process 1319793 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train_matryoshka_cot.py', '--local_rank=6', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Llama-3.1-8B-Instruct', '--data_name', '/data_x/junkim100/projects/matryoshka_cot/data/10k_cot_collection.jsonl', '--wb_project', 'matryoshka_cot', '--wb_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_dir', './output', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.0', '--warmup_ratio', '0.0', '--lr_scheduler_type', 'cosine', '--bf16', 'true', '--tf32', 'true', '--gradient_checkpointing', 'true', '--logging_steps', '1']
[2025-07-08 21:24:35,319] [INFO] [launch.py:256:main] process 1319794 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train_matryoshka_cot.py', '--local_rank=7', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Llama-3.1-8B-Instruct', '--data_name', '/data_x/junkim100/projects/matryoshka_cot/data/10k_cot_collection.jsonl', '--wb_project', 'matryoshka_cot', '--wb_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_dir', './output', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.0', '--warmup_ratio', '0.0', '--lr_scheduler_type', 'cosine', '--bf16', 'true', '--tf32', 'true', '--gradient_checkpointing', 'true', '--logging_steps', '1']
[2025-07-08 21:24:40,765] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 21:24:40,785] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 21:24:40,871] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 21:24:40,998] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 21:24:41,010] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 21:24:41,012] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 21:24:41,015] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 21:24:41,019] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 21:24:42,275] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-08 21:24:42,377] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-08 21:24:42,467] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-08 21:24:42,476] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-08 21:24:42,484] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-08 21:24:42,485] [INFO] [comm.py:700:init_distributed] Initializing TorchBackend in DeepSpeed with backend nccl
[2025-07-08 21:24:42,529] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-08 21:24:42,582] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-08 21:24:43,015] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-08 21:24:44,480] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-08 21:24:44,574] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-08 21:24:44,603] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-08 21:24:44,603] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-08 21:24:44,613] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-08 21:24:44,622] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-08 21:24:44,837] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-08 21:24:44,913] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-08 21:25:01,951] [INFO] [partition_parameters.py:348:__exit__] finished initializing model - num_params = 291, num_elems = 8.03B

Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]
Loading checkpoint shards:  25%|██▌       | 1/4 [00:48<02:24, 48.08s/it]
Loading checkpoint shards:  25%|██▌       | 1/4 [00:48<02:24, 48.09s/it]
Loading checkpoint shards:  25%|██▌       | 1/4 [00:48<02:24, 48.09s/it]
Loading checkpoint shards:  25%|██▌       | 1/4 [00:48<02:24, 48.09s/it]
Loading checkpoint shards:  25%|██▌       | 1/4 [00:48<02:24, 48.09s/it]
Loading checkpoint shards:  25%|██▌       | 1/4 [00:48<02:24, 48.08s/it]
Loading checkpoint shards:  25%|██▌       | 1/4 [00:48<02:24, 48.09s/it]
Loading checkpoint shards:  25%|██▌       | 1/4 [00:48<02:24, 48.29s/it]
Loading checkpoint shards:  50%|█████     | 2/4 [01:36<01:36, 48.04s/it]
Loading checkpoint shards:  50%|█████     | 2/4 [01:36<01:36, 48.04s/it]
Loading checkpoint shards:  50%|█████     | 2/4 [01:36<01:36, 48.05s/it]
Loading checkpoint shards:  50%|█████     | 2/4 [01:36<01:36, 48.04s/it]
Loading checkpoint shards:  50%|█████     | 2/4 [01:36<01:36, 48.05s/it]
Loading checkpoint shards:  50%|█████     | 2/4 [01:36<01:36, 48.05s/it]
Loading checkpoint shards:  50%|█████     | 2/4 [01:36<01:36, 48.05s/it]
Loading checkpoint shards:  50%|█████     | 2/4 [01:36<01:36, 48.15s/it]
Loading checkpoint shards:  75%|███████▌  | 3/4 [02:39<00:54, 54.99s/it]
Loading checkpoint shards:  75%|███████▌  | 3/4 [02:39<00:54, 55.00s/it]
Loading checkpoint shards:  75%|███████▌  | 3/4 [02:39<00:54, 55.00s/it]
Loading checkpoint shards:  75%|███████▌  | 3/4 [02:39<00:54, 55.00s/it]
Loading checkpoint shards:  75%|███████▌  | 3/4 [02:39<00:54, 55.00s/it]
Loading checkpoint shards:  75%|███████▌  | 3/4 [02:39<00:54, 55.00s/it]
Loading checkpoint shards:  75%|███████▌  | 3/4 [02:39<00:54, 55.00s/it]
Loading checkpoint shards:  75%|███████▌  | 3/4 [02:39<00:55, 55.03s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [02:51<00:00, 37.94s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [02:51<00:00, 42.79s/it]

Loading checkpoint shards: 100%|██████████| 4/4 [02:51<00:00, 37.94s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [02:51<00:00, 42.79s/it]

Loading checkpoint shards: 100%|██████████| 4/4 [02:51<00:00, 37.94s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [02:51<00:00, 42.79s/it]

Loading checkpoint shards: 100%|██████████| 4/4 [02:51<00:00, 37.94s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [02:51<00:00, 37.94s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [02:51<00:00, 42.79s/it]

Loading checkpoint shards: 100%|██████████| 4/4 [02:51<00:00, 42.79s/it]

Loading checkpoint shards: 100%|██████████| 4/4 [02:51<00:00, 37.94s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [02:51<00:00, 42.79s/it]

Loading checkpoint shards: 100%|██████████| 4/4 [02:51<00:00, 37.94s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [02:51<00:00, 42.79s/it]

Loading checkpoint shards: 100%|██████████| 4/4 [02:51<00:00, 37.91s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [02:51<00:00, 42.81s/it]
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
2025-07-08 21:28:00 | WARNING | Loading dataset…
2025-07-08 21:28:00 | WARNING | Loading dataset…
2025-07-08 21:28:00 | WARNING | Loading dataset…
2025-07-08 21:28:00 | WARNING | Loading dataset…
2025-07-08 21:28:00 | WARNING | Loading dataset…
2025-07-08 21:28:00 | WARNING | Loading dataset…
2025-07-08 21:28:00 | WARNING | Loading dataset…
2025-07-08 21:28:00 | WARNING | Loading dataset…
2025-07-08 21:28:05 | WARNING | Prepared 10,000 samples.
/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py:189: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `MatryoshkaTrainer.__init__`. Use `processing_class` instead.
  super().__init__(**kw)
2025-07-08 21:28:05 | WARNING | Prepared 10,000 samples.
/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py:189: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `MatryoshkaTrainer.__init__`. Use `processing_class` instead.
  super().__init__(**kw)
2025-07-08 21:28:05 | WARNING | Prepared 10,000 samples.
/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py:189: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `MatryoshkaTrainer.__init__`. Use `processing_class` instead.
  super().__init__(**kw)
2025-07-08 21:28:05 | WARNING | Prepared 10,000 samples.
/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py:189: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `MatryoshkaTrainer.__init__`. Use `processing_class` instead.
  super().__init__(**kw)
2025-07-08 21:28:05 | WARNING | Prepared 10,000 samples.
/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py:189: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `MatryoshkaTrainer.__init__`. Use `processing_class` instead.
  super().__init__(**kw)
2025-07-08 21:28:05 | WARNING | Prepared 10,000 samples.
/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py:189: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `MatryoshkaTrainer.__init__`. Use `processing_class` instead.
  super().__init__(**kw)
2025-07-08 21:28:05 | WARNING | Prepared 10,000 samples.
/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py:189: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `MatryoshkaTrainer.__init__`. Use `processing_class` instead.
  super().__init__(**kw)
2025-07-08 21:28:05 | WARNING | Prepared 10,000 samples.
/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py:189: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `MatryoshkaTrainer.__init__`. Use `processing_class` instead.
  super().__init__(**kw)
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-openmath-Llama-3.1-8B-Instruct; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-openmath-Llama-3.1-8B-Instruct; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-openmath-Llama-3.1-8B-Instruct; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-openmath-Llama-3.1-8B-Instruct; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-openmath-Llama-3.1-8B-Instruct; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-openmath-Llama-3.1-8B-Instruct; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-openmath-Llama-3.1-8B-Instruct; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-openmath-Llama-3.1-8B-Instruct; so disabling `WANDB_LOG_MODEL`
Parameter Offload: Total persistent parameters: 266240 in 65 params
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
