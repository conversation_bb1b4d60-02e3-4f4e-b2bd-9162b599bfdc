[2025-07-08 21:30:46,681] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 21:30:49,658] [WARNING] [runner.py:215:fetch_hostfile] Unable to find hostfile, will proceed with training with local resources only.
Detected VISIBLE_DEVICES=0,1,2,3,4,5,6,7: setting --include=localhost:0,1,2,3,4,5,6,7
[2025-07-08 21:30:49,658] [INFO] [runner.py:605:main] cmd = /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10 -u -m deepspeed.launcher.launch --world_info=eyJsb2NhbGhvc3QiOiBbMCwgMSwgMiwgMywgNCwgNSwgNiwgN119 --master_addr=127.0.0.1 --master_port=49056 --enable_each_rank_log=None train_matryoshka_cot.py --deepspeed deepspeed3.json --proctitle junkim100 --model_name_or_path Llama-3.1-8B-Instruct --data_name /data_x/junkim100/projects/matryoshka_cot/data/10k_cot_collection.jsonl --wb_project matryoshka_cot --wb_name matryoshka-openmath-Llama-3.1-8B-Instruct --output_name matryoshka-openmath-Llama-3.1-8B-Instruct --output_dir ./output --max_length 16384 --num_train_epochs 2 --per_device_train_batch_size 1 --per_device_eval_batch_size 1 --gradient_accumulation_steps 1 --save_only_model --learning_rate 1e-5 --weight_decay 0.0 --warmup_ratio 0.0 --lr_scheduler_type cosine --bf16 true --tf32 true --gradient_checkpointing true --logging_steps 1
[2025-07-08 21:30:51,278] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 21:30:54,067] [INFO] [launch.py:146:main] WORLD INFO DICT: {'localhost': [0, 1, 2, 3, 4, 5, 6, 7]}
[2025-07-08 21:30:54,067] [INFO] [launch.py:152:main] nnodes=1, num_local_procs=8, node_rank=0
[2025-07-08 21:30:54,067] [INFO] [launch.py:163:main] global_rank_mapping=defaultdict(<class 'list'>, {'localhost': [0, 1, 2, 3, 4, 5, 6, 7]})
[2025-07-08 21:30:54,067] [INFO] [launch.py:164:main] dist_world_size=8
[2025-07-08 21:30:54,067] [INFO] [launch.py:168:main] Setting CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
[2025-07-08 21:30:54,068] [INFO] [launch.py:256:main] process 1327245 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train_matryoshka_cot.py', '--local_rank=0', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Llama-3.1-8B-Instruct', '--data_name', '/data_x/junkim100/projects/matryoshka_cot/data/10k_cot_collection.jsonl', '--wb_project', 'matryoshka_cot', '--wb_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_dir', './output', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.0', '--warmup_ratio', '0.0', '--lr_scheduler_type', 'cosine', '--bf16', 'true', '--tf32', 'true', '--gradient_checkpointing', 'true', '--logging_steps', '1']
[2025-07-08 21:30:54,069] [INFO] [launch.py:256:main] process 1327246 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train_matryoshka_cot.py', '--local_rank=1', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Llama-3.1-8B-Instruct', '--data_name', '/data_x/junkim100/projects/matryoshka_cot/data/10k_cot_collection.jsonl', '--wb_project', 'matryoshka_cot', '--wb_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_dir', './output', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.0', '--warmup_ratio', '0.0', '--lr_scheduler_type', 'cosine', '--bf16', 'true', '--tf32', 'true', '--gradient_checkpointing', 'true', '--logging_steps', '1']
[2025-07-08 21:30:54,070] [INFO] [launch.py:256:main] process 1327247 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train_matryoshka_cot.py', '--local_rank=2', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Llama-3.1-8B-Instruct', '--data_name', '/data_x/junkim100/projects/matryoshka_cot/data/10k_cot_collection.jsonl', '--wb_project', 'matryoshka_cot', '--wb_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_dir', './output', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.0', '--warmup_ratio', '0.0', '--lr_scheduler_type', 'cosine', '--bf16', 'true', '--tf32', 'true', '--gradient_checkpointing', 'true', '--logging_steps', '1']
[2025-07-08 21:30:54,071] [INFO] [launch.py:256:main] process 1327248 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train_matryoshka_cot.py', '--local_rank=3', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Llama-3.1-8B-Instruct', '--data_name', '/data_x/junkim100/projects/matryoshka_cot/data/10k_cot_collection.jsonl', '--wb_project', 'matryoshka_cot', '--wb_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_dir', './output', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.0', '--warmup_ratio', '0.0', '--lr_scheduler_type', 'cosine', '--bf16', 'true', '--tf32', 'true', '--gradient_checkpointing', 'true', '--logging_steps', '1']
[2025-07-08 21:30:54,072] [INFO] [launch.py:256:main] process 1327249 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train_matryoshka_cot.py', '--local_rank=4', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Llama-3.1-8B-Instruct', '--data_name', '/data_x/junkim100/projects/matryoshka_cot/data/10k_cot_collection.jsonl', '--wb_project', 'matryoshka_cot', '--wb_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_dir', './output', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.0', '--warmup_ratio', '0.0', '--lr_scheduler_type', 'cosine', '--bf16', 'true', '--tf32', 'true', '--gradient_checkpointing', 'true', '--logging_steps', '1']
[2025-07-08 21:30:54,072] [INFO] [launch.py:256:main] process 1327250 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train_matryoshka_cot.py', '--local_rank=5', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Llama-3.1-8B-Instruct', '--data_name', '/data_x/junkim100/projects/matryoshka_cot/data/10k_cot_collection.jsonl', '--wb_project', 'matryoshka_cot', '--wb_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_dir', './output', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.0', '--warmup_ratio', '0.0', '--lr_scheduler_type', 'cosine', '--bf16', 'true', '--tf32', 'true', '--gradient_checkpointing', 'true', '--logging_steps', '1']
[2025-07-08 21:30:54,073] [INFO] [launch.py:256:main] process 1327251 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train_matryoshka_cot.py', '--local_rank=6', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Llama-3.1-8B-Instruct', '--data_name', '/data_x/junkim100/projects/matryoshka_cot/data/10k_cot_collection.jsonl', '--wb_project', 'matryoshka_cot', '--wb_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_dir', './output', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.0', '--warmup_ratio', '0.0', '--lr_scheduler_type', 'cosine', '--bf16', 'true', '--tf32', 'true', '--gradient_checkpointing', 'true', '--logging_steps', '1']
[2025-07-08 21:30:54,074] [INFO] [launch.py:256:main] process 1327252 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train_matryoshka_cot.py', '--local_rank=7', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Llama-3.1-8B-Instruct', '--data_name', '/data_x/junkim100/projects/matryoshka_cot/data/10k_cot_collection.jsonl', '--wb_project', 'matryoshka_cot', '--wb_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_dir', './output', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.0', '--warmup_ratio', '0.0', '--lr_scheduler_type', 'cosine', '--bf16', 'true', '--tf32', 'true', '--gradient_checkpointing', 'true', '--logging_steps', '1']
[2025-07-08 21:30:58,921] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 21:30:58,927] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 21:30:59,029] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 21:30:59,032] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 21:30:59,034] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 21:30:59,035] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 21:30:59,036] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 21:30:59,040] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-08 21:31:00,363] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-08 21:31:00,464] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-08 21:31:00,464] [INFO] [comm.py:700:init_distributed] Initializing TorchBackend in DeepSpeed with backend nccl
[2025-07-08 21:31:00,470] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-08 21:31:00,511] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-08 21:31:00,511] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-08 21:31:00,548] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-08 21:31:00,552] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-08 21:31:00,824] [INFO] [comm.py:669:init_distributed] cdb=None
[2025-07-08 21:31:02,348] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-08 21:31:02,407] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-08 21:31:02,412] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-08 21:31:02,412] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-08 21:31:02,437] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-08 21:31:02,455] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-08 21:31:02,462] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-08 21:31:02,464] [INFO] [config.py:735:__init__] Config mesh_device None world_size = 8
[2025-07-08 21:31:18,827] [INFO] [partition_parameters.py:348:__exit__] finished initializing model - num_params = 291, num_elems = 8.03B

Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]
Loading checkpoint shards:  25%|██▌       | 1/4 [00:05<00:16,  5.41s/it]
Loading checkpoint shards:  25%|██▌       | 1/4 [00:05<00:16,  5.40s/it]
Loading checkpoint shards:  25%|██▌       | 1/4 [00:05<00:16,  5.41s/it]
Loading checkpoint shards:  25%|██▌       | 1/4 [00:05<00:16,  5.40s/it]
Loading checkpoint shards:  25%|██▌       | 1/4 [00:05<00:16,  5.41s/it]
Loading checkpoint shards:  25%|██▌       | 1/4 [00:05<00:16,  5.41s/it]
Loading checkpoint shards:  25%|██▌       | 1/4 [00:05<00:16,  5.40s/it]
Loading checkpoint shards:  25%|██▌       | 1/4 [00:05<00:16,  5.61s/it]
Loading checkpoint shards:  50%|█████     | 2/4 [00:10<00:10,  5.40s/it]
Loading checkpoint shards:  50%|█████     | 2/4 [00:10<00:10,  5.40s/it]
Loading checkpoint shards:  50%|█████     | 2/4 [00:10<00:10,  5.40s/it]
Loading checkpoint shards:  50%|█████     | 2/4 [00:10<00:10,  5.40s/it]
Loading checkpoint shards:  50%|█████     | 2/4 [00:10<00:10,  5.40s/it]
Loading checkpoint shards:  50%|█████     | 2/4 [00:10<00:10,  5.40s/it]
Loading checkpoint shards:  50%|█████     | 2/4 [00:10<00:10,  5.40s/it]
Loading checkpoint shards:  50%|█████     | 2/4 [00:11<00:10,  5.49s/it]
Loading checkpoint shards:  75%|███████▌  | 3/4 [00:16<00:05,  5.50s/it]
Loading checkpoint shards:  75%|███████▌  | 3/4 [00:16<00:05,  5.50s/it]
Loading checkpoint shards:  75%|███████▌  | 3/4 [00:16<00:05,  5.50s/it]
Loading checkpoint shards:  75%|███████▌  | 3/4 [00:16<00:05,  5.50s/it]
Loading checkpoint shards:  75%|███████▌  | 3/4 [00:16<00:05,  5.50s/it]
Loading checkpoint shards:  75%|███████▌  | 3/4 [00:16<00:05,  5.50s/it]
Loading checkpoint shards:  75%|███████▌  | 3/4 [00:16<00:05,  5.50s/it]
Loading checkpoint shards:  75%|███████▌  | 3/4 [00:16<00:05,  5.58s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [00:18<00:00,  4.02s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [00:18<00:00,  4.54s/it]

Loading checkpoint shards: 100%|██████████| 4/4 [00:18<00:00,  4.02s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [00:18<00:00,  4.02s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [00:18<00:00,  4.54s/it]

Loading checkpoint shards: 100%|██████████| 4/4 [00:18<00:00,  4.54s/it]

Loading checkpoint shards: 100%|██████████| 4/4 [00:18<00:00,  4.02s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [00:18<00:00,  4.54s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [00:18<00:00,  4.02s/it]

Loading checkpoint shards: 100%|██████████| 4/4 [00:18<00:00,  4.54s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [00:18<00:00,  4.02s/it]

Loading checkpoint shards: 100%|██████████| 4/4 [00:18<00:00,  4.54s/it]

Loading checkpoint shards: 100%|██████████| 4/4 [00:18<00:00,  4.02s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [00:18<00:00,  4.54s/it]

Loading checkpoint shards: 100%|██████████| 4/4 [00:18<00:00,  3.97s/it]
Loading checkpoint shards: 100%|██████████| 4/4 [00:18<00:00,  4.55s/it]
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
2025-07-08 21:31:44 | WARNING | Loading dataset…
2025-07-08 21:31:44 | WARNING | Loading dataset…
2025-07-08 21:31:44 | WARNING | Loading dataset…
2025-07-08 21:31:44 | WARNING | Loading dataset…
2025-07-08 21:31:44 | WARNING | Loading dataset…
2025-07-08 21:31:44 | WARNING | Loading dataset…
2025-07-08 21:31:44 | WARNING | Loading dataset…
2025-07-08 21:31:44 | WARNING | Loading dataset…
2025-07-08 21:31:49 | WARNING | Prepared 10,000 samples.
/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py:197: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `MatryoshkaTrainer.__init__`. Use `processing_class` instead.
  super().__init__(**kw)
2025-07-08 21:31:49 | WARNING | Prepared 10,000 samples.
/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py:197: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `MatryoshkaTrainer.__init__`. Use `processing_class` instead.
  super().__init__(**kw)
2025-07-08 21:31:50 | WARNING | Prepared 10,000 samples.
/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py:197: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `MatryoshkaTrainer.__init__`. Use `processing_class` instead.
  super().__init__(**kw)
2025-07-08 21:31:50 | WARNING | Prepared 10,000 samples.
/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py:197: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `MatryoshkaTrainer.__init__`. Use `processing_class` instead.
  super().__init__(**kw)
2025-07-08 21:31:50 | WARNING | Prepared 10,000 samples.
2025-07-08 21:31:50 | WARNING | Prepared 10,000 samples.
/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py:197: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `MatryoshkaTrainer.__init__`. Use `processing_class` instead.
  super().__init__(**kw)
/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py:197: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `MatryoshkaTrainer.__init__`. Use `processing_class` instead.
  super().__init__(**kw)
2025-07-08 21:31:50 | WARNING | Prepared 10,000 samples.
/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py:197: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `MatryoshkaTrainer.__init__`. Use `processing_class` instead.
  super().__init__(**kw)
2025-07-08 21:31:50 | WARNING | Prepared 10,000 samples.
/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py:197: FutureWarning: `tokenizer` is deprecated and will be removed in version 5.0.0 for `MatryoshkaTrainer.__init__`. Use `processing_class` instead.
  super().__init__(**kw)
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-openmath-Llama-3.1-8B-Instruct; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-openmath-Llama-3.1-8B-Instruct; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-openmath-Llama-3.1-8B-Instruct; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-openmath-Llama-3.1-8B-Instruct; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-openmath-Llama-3.1-8B-Instruct; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-openmath-Llama-3.1-8B-Instruct; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-openmath-Llama-3.1-8B-Instruct; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-openmath-Llama-3.1-8B-Instruct; so disabling `WANDB_LOG_MODEL`
Parameter Offload: Total persistent parameters: 266240 in 65 params
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[2025-07-08 21:32:26,103] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
[2025-07-08 21:32:26,103] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
[2025-07-08 21:32:26,104] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
[2025-07-08 21:32:26,106] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
[2025-07-08 21:32:26,106] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
[2025-07-08 21:32:26,107] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
[2025-07-08 21:32:26,107] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
[2025-07-08 21:32:26,357] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started
[rank7]: Traceback (most recent call last):
[rank7]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 313, in <module>
[rank7]:     train(m_args, d_args, t_args)
[rank7]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 287, in train
[rank7]:     trainer.train()
[rank7]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2240, in train
[rank7]:     return inner_training_loop(
[rank7]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2509, in _inner_training_loop
[rank7]:     batch_samples, num_items_in_batch = self.get_batch_samples(epoch_iterator, num_batches, args.device)
[rank7]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 5263, in get_batch_samples
[rank7]:     batch_samples.append(next(epoch_iterator))
[rank7]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/accelerate/data_loader.py", line 567, in __iter__
[rank7]:     current_batch = next(dataloader_iter)
[rank7]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 733, in __next__
[rank7]:     data = self._next_data()
[rank7]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 789, in _next_data
[rank7]:     data = self._dataset_fetcher.fetch(index)  # may raise StopIteration
[rank7]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/_utils/fetch.py", line 55, in fetch
[rank7]:     return self.collate_fn(data)
[rank7]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer_utils.py", line 872, in __call__
[rank7]:     return self.data_collator(features)
[rank7]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 181, in __call__
[rank7]:     prompt_ids = [b["prompt_ids"] for b in batch]
[rank7]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 181, in <listcomp>
[rank7]:     prompt_ids = [b["prompt_ids"] for b in batch]
[rank7]: KeyError: 'prompt_ids'
[rank1]: Traceback (most recent call last):
[rank1]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 313, in <module>
[rank1]:     train(m_args, d_args, t_args)
[rank1]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 287, in train
[rank1]:     trainer.train()
[rank1]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2240, in train
[rank1]:     return inner_training_loop(
[rank1]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2509, in _inner_training_loop
[rank1]:     batch_samples, num_items_in_batch = self.get_batch_samples(epoch_iterator, num_batches, args.device)
[rank1]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 5263, in get_batch_samples
[rank1]:     batch_samples.append(next(epoch_iterator))
[rank1]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/accelerate/data_loader.py", line 567, in __iter__
[rank1]:     current_batch = next(dataloader_iter)
[rank1]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 733, in __next__
[rank1]:     data = self._next_data()
[rank1]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 789, in _next_data
[rank1]:     data = self._dataset_fetcher.fetch(index)  # may raise StopIteration
[rank1]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/_utils/fetch.py", line 55, in fetch
[rank1]:     return self.collate_fn(data)
[rank1]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer_utils.py", line 872, in __call__
[rank1]:     return self.data_collator(features)
[rank1]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 181, in __call__
[rank1]:     prompt_ids = [b["prompt_ids"] for b in batch]
[rank1]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 181, in <listcomp>
[rank1]:     prompt_ids = [b["prompt_ids"] for b in batch]
[rank1]: KeyError: 'prompt_ids'
[rank6]: Traceback (most recent call last):
[rank6]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 313, in <module>
[rank6]:     train(m_args, d_args, t_args)
[rank6]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 287, in train
[rank6]:     trainer.train()
[rank6]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2240, in train
[rank6]:     return inner_training_loop(
[rank6]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2509, in _inner_training_loop
[rank6]:     batch_samples, num_items_in_batch = self.get_batch_samples(epoch_iterator, num_batches, args.device)
[rank6]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 5263, in get_batch_samples
[rank6]:     batch_samples.append(next(epoch_iterator))
[rank6]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/accelerate/data_loader.py", line 567, in __iter__
[rank6]:     current_batch = next(dataloader_iter)
[rank6]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 733, in __next__
[rank6]:     data = self._next_data()
[rank6]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 789, in _next_data
[rank6]:     data = self._dataset_fetcher.fetch(index)  # may raise StopIteration
[rank6]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/_utils/fetch.py", line 55, in fetch
[rank6]:     return self.collate_fn(data)
[rank6]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer_utils.py", line 872, in __call__
[rank6]:     return self.data_collator(features)
[rank6]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 181, in __call__
[rank6]:     prompt_ids = [b["prompt_ids"] for b in batch]
[rank6]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 181, in <listcomp>
[rank6]:     prompt_ids = [b["prompt_ids"] for b in batch]
[rank6]: KeyError: 'prompt_ids'
[rank5]: Traceback (most recent call last):
[rank5]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 313, in <module>
[rank5]:     train(m_args, d_args, t_args)
[rank5]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 287, in train
[rank5]:     trainer.train()
[rank5]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2240, in train
[rank5]:     return inner_training_loop(
[rank5]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2509, in _inner_training_loop
[rank5]:     batch_samples, num_items_in_batch = self.get_batch_samples(epoch_iterator, num_batches, args.device)
[rank5]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 5263, in get_batch_samples
[rank5]:     batch_samples.append(next(epoch_iterator))
[rank5]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/accelerate/data_loader.py", line 567, in __iter__
[rank5]:     current_batch = next(dataloader_iter)
[rank5]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 733, in __next__
[rank5]:     data = self._next_data()
[rank5]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 789, in _next_data
[rank5]:     data = self._dataset_fetcher.fetch(index)  # may raise StopIteration
[rank5]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/_utils/fetch.py", line 55, in fetch
[rank5]:     return self.collate_fn(data)
[rank5]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer_utils.py", line 872, in __call__
[rank5]:     return self.data_collator(features)
[rank5]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 181, in __call__
[rank5]:     prompt_ids = [b["prompt_ids"] for b in batch]
[rank5]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 181, in <listcomp>
[rank5]:     prompt_ids = [b["prompt_ids"] for b in batch]
[rank5]: KeyError: 'prompt_ids'
[rank4]: Traceback (most recent call last):
[rank4]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 313, in <module>
[rank4]:     train(m_args, d_args, t_args)
[rank4]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 287, in train
[rank4]:     trainer.train()
[rank4]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2240, in train
[rank4]:     return inner_training_loop(
[rank4]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2509, in _inner_training_loop
[rank4]:     batch_samples, num_items_in_batch = self.get_batch_samples(epoch_iterator, num_batches, args.device)
[rank4]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 5263, in get_batch_samples
[rank4]:     batch_samples.append(next(epoch_iterator))
[rank4]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/accelerate/data_loader.py", line 567, in __iter__
[rank4]:     current_batch = next(dataloader_iter)
[rank4]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 733, in __next__
[rank4]:     data = self._next_data()
[rank4]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 789, in _next_data
[rank4]:     data = self._dataset_fetcher.fetch(index)  # may raise StopIteration
[rank4]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/_utils/fetch.py", line 55, in fetch
[rank4]:     return self.collate_fn(data)
[rank4]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer_utils.py", line 872, in __call__
[rank4]:     return self.data_collator(features)
[rank4]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 181, in __call__
[rank4]:     prompt_ids = [b["prompt_ids"] for b in batch]
[rank4]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 181, in <listcomp>
[rank4]:     prompt_ids = [b["prompt_ids"] for b in batch]
[rank4]: KeyError: 'prompt_ids'
[rank3]: Traceback (most recent call last):
[rank3]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 313, in <module>
[rank3]:     train(m_args, d_args, t_args)
[rank3]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 287, in train
[rank3]:     trainer.train()
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2240, in train
[rank3]:     return inner_training_loop(
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2509, in _inner_training_loop
[rank3]:     batch_samples, num_items_in_batch = self.get_batch_samples(epoch_iterator, num_batches, args.device)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 5263, in get_batch_samples
[rank3]:     batch_samples.append(next(epoch_iterator))
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/accelerate/data_loader.py", line 567, in __iter__
[rank3]:     current_batch = next(dataloader_iter)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 733, in __next__
[rank3]:     data = self._next_data()
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 789, in _next_data
[rank3]:     data = self._dataset_fetcher.fetch(index)  # may raise StopIteration
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/_utils/fetch.py", line 55, in fetch
[rank3]:     return self.collate_fn(data)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer_utils.py", line 872, in __call__
[rank3]:     return self.data_collator(features)
[rank3]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 181, in __call__
[rank3]:     prompt_ids = [b["prompt_ids"] for b in batch]
[rank3]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 181, in <listcomp>
[rank3]:     prompt_ids = [b["prompt_ids"] for b in batch]
[rank3]: KeyError: 'prompt_ids'
[rank2]: Traceback (most recent call last):
[rank2]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 313, in <module>
[rank2]:     train(m_args, d_args, t_args)
[rank2]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 287, in train
[rank2]:     trainer.train()
[rank2]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2240, in train
[rank2]:     return inner_training_loop(
[rank2]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2509, in _inner_training_loop
[rank2]:     batch_samples, num_items_in_batch = self.get_batch_samples(epoch_iterator, num_batches, args.device)
[rank2]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 5263, in get_batch_samples
[rank2]:     batch_samples.append(next(epoch_iterator))
[rank2]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/accelerate/data_loader.py", line 567, in __iter__
[rank2]:     current_batch = next(dataloader_iter)
[rank2]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 733, in __next__
[rank2]:     data = self._next_data()
[rank2]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 789, in _next_data
[rank2]:     data = self._dataset_fetcher.fetch(index)  # may raise StopIteration
[rank2]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/_utils/fetch.py", line 55, in fetch
[rank2]:     return self.collate_fn(data)
[rank2]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer_utils.py", line 872, in __call__
[rank2]:     return self.data_collator(features)
[rank2]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 181, in __call__
[rank2]:     prompt_ids = [b["prompt_ids"] for b in batch]
[rank2]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 181, in <listcomp>
[rank2]:     prompt_ids = [b["prompt_ids"] for b in batch]
[rank2]: KeyError: 'prompt_ids'
wandb: Currently logged in as: junkim100 (junkim) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.21.0
wandb: Run data is saved locally in /data_x/junkim100/projects/matryoshka_cot/wandb/run-20250708_213227-wnn4h3wx
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run ./output
wandb: ⭐️ View project at https://wandb.ai/junkim/matryoshka_cot
wandb: 🚀 View run at https://wandb.ai/junkim/matryoshka_cot/runs/wnn4h3wx

  0%|          | 0/2500 [00:00<?, ?it/s]Traceback (most recent call last):
  File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 313, in <module>
    train(m_args, d_args, t_args)
  File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 287, in train
    trainer.train()
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2240, in train
    return inner_training_loop(
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2509, in _inner_training_loop
    batch_samples, num_items_in_batch = self.get_batch_samples(epoch_iterator, num_batches, args.device)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 5263, in get_batch_samples
    batch_samples.append(next(epoch_iterator))
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/accelerate/data_loader.py", line 567, in __iter__
    current_batch = next(dataloader_iter)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 733, in __next__
    data = self._next_data()
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 789, in _next_data
    data = self._dataset_fetcher.fetch(index)  # may raise StopIteration
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/_utils/fetch.py", line 55, in fetch
    return self.collate_fn(data)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer_utils.py", line 872, in __call__
    return self.data_collator(features)
  File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 181, in __call__
    prompt_ids = [b["prompt_ids"] for b in batch]
  File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 181, in <listcomp>
    prompt_ids = [b["prompt_ids"] for b in batch]
KeyError: 'prompt_ids'
[rank0]: Traceback (most recent call last):
[rank0]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 313, in <module>
[rank0]:     train(m_args, d_args, t_args)
[rank0]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 287, in train
[rank0]:     trainer.train()
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2240, in train
[rank0]:     return inner_training_loop(
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2509, in _inner_training_loop
[rank0]:     batch_samples, num_items_in_batch = self.get_batch_samples(epoch_iterator, num_batches, args.device)
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 5263, in get_batch_samples
[rank0]:     batch_samples.append(next(epoch_iterator))
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/accelerate/data_loader.py", line 567, in __iter__
[rank0]:     current_batch = next(dataloader_iter)
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 733, in __next__
[rank0]:     data = self._next_data()
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 789, in _next_data
[rank0]:     data = self._dataset_fetcher.fetch(index)  # may raise StopIteration
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/utils/data/_utils/fetch.py", line 55, in fetch
[rank0]:     return self.collate_fn(data)
[rank0]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer_utils.py", line 872, in __call__
[rank0]:     return self.data_collator(features)
[rank0]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 181, in __call__
[rank0]:     prompt_ids = [b["prompt_ids"] for b in batch]
[rank0]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 181, in <listcomp>
[rank0]:     prompt_ids = [b["prompt_ids"] for b in batch]
[rank0]: KeyError: 'prompt_ids'
[1;34mwandb[0m: 
[1;34mwandb[0m: 🚀 View run [33m./output[0m at: [34mhttps://wandb.ai/junkim/matryoshka_cot/runs/wnn4h3wx[0m
[1;34mwandb[0m: Find logs at: [1;35mwandb/run-20250708_213227-wnn4h3wx/logs[0m
[2025-07-08 21:32:33,197] [INFO] [launch.py:319:sigkill_handler] Killing subprocess 1327245
[2025-07-08 21:32:45,963] [INFO] [launch.py:319:sigkill_handler] Killing subprocess 1327246
[2025-07-08 21:32:45,963] [INFO] [launch.py:319:sigkill_handler] Killing subprocess 1327247
[2025-07-08 21:32:45,997] [INFO] [launch.py:319:sigkill_handler] Killing subprocess 1327248
[2025-07-08 21:32:46,022] [INFO] [launch.py:319:sigkill_handler] Killing subprocess 1327249
[2025-07-08 21:32:46,045] [INFO] [launch.py:319:sigkill_handler] Killing subprocess 1327250
[2025-07-08 21:32:46,068] [INFO] [launch.py:319:sigkill_handler] Killing subprocess 1327251
[2025-07-08 21:32:46,091] [INFO] [launch.py:319:sigkill_handler] Killing subprocess 1327252
[2025-07-08 21:32:46,113] [ERROR] [launch.py:325:sigkill_handler] ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train_matryoshka_cot.py', '--local_rank=7', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'Llama-3.1-8B-Instruct', '--data_name', '/data_x/junkim100/projects/matryoshka_cot/data/10k_cot_collection.jsonl', '--wb_project', 'matryoshka_cot', '--wb_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_name', 'matryoshka-openmath-Llama-3.1-8B-Instruct', '--output_dir', './output', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.0', '--warmup_ratio', '0.0', '--lr_scheduler_type', 'cosine', '--bf16', 'true', '--tf32', 'true', '--gradient_checkpointing', 'true', '--logging_steps', '1'] exits with return code = 1
