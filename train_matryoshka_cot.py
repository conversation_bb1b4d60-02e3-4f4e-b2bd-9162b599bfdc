"""
train_matryoshka_cot.py
—————————————————————————————————————————————————————————
End-to-end fine-tuning script for Matryoshka-style Chain-of-Thought
training on decoder-only LLMs.

• Reads a JSONL file where each line is
  { "user": "...", "assistant": "reasoning … final answer" }.

• Applies on-the-fly depth slicing (0, 2, 4, 8 sentences by default)
  and computes a weighted sum of XE losses so the model can
  “exit early” for easy problems.

—————————————————————————————————————————————————————————
"""

import os, json, re, copy, logging
from dataclasses import dataclass, field
from typing import Dict, List, Sequence, Optional, Tuple

import numpy as np
import torch
from torch.utils.data import Dataset
import transformers
from transformers import Trainer
from datasets import load_from_disk
from setproctitle import setproctitle

# ╭─────────────────────────── constants ───────────────────────────╮
IGNORE_INDEX = -100
DEFAULT_PAD_TOKEN = "[PAD]"
DEFAULT_EOS_TOKEN = "</s>"
DEFAULT_BOS_TOKEN = "</s>"
DEFAULT_UNK_TOKEN = "</s>"


# ╭──────────────────────── dataclass args ─────────────────────────╮
@dataclass
class ModelArguments:
    model_name_or_path: str = field(default="Llama-3.1-8B-Instruct")
    output_name: str = field(default="matryoshka_cot")


@dataclass
class DataArguments:
    data_name: str = field(
        default="combined_cot_openmath.jsonl",
        metadata={"help": "JSONL file or HF dataset path"},
    )
    max_length: int = field(
        default=4096, metadata={"help": "Maximum total sequence length"}
    )

    # ╭─────────────────── Matryoshka Depth Configuration ───────────────╮
    num_depths: int = field(
        default=4,
        metadata={
            "help": "Number of reasoning depths to use (e.g., 4 creates depths based on data)"
        },
    )
    use_sentence_depths: bool = field(
        default=True,
        metadata={
            "help": "If True, use sentence-based depths (0,2,4,8). If False, use length-based depths."
        },
    )
    custom_depths: Optional[str] = field(
        default=None,
        metadata={
            "help": "Custom depths as comma-separated values (e.g., '0,2,4,8'). Overrides other depth settings."
        },
    )


@dataclass
class TrainingArguments(transformers.TrainingArguments):
    # ╭─────────────────── Wandb & Logging ───────────────────╮
    report_to: str = field(default="wandb")
    wb_name: str = field(default="matryoshka-run")
    wb_project: str = field(default="matryoshka-cot")
    run_name: Optional[str] = field(default=None)  # Will be set to wb_name if None
    proctitle: str = field(default="matryoshka-cot")
    logging_steps: int = field(default=10)

    # ╭─────────────────── Model & Training ──────────────────╮
    cache_dir: Optional[str] = field(default=None)
    optim: str = field(
        default="adamw_torch",
        metadata={"help": "adamw_torch, paged_adamw_32bit, adamw_bnb_8bit"},
    )
    deepspeed: str = field(default=None)

    # ╭─────────────────── Precision & Performance ───────────╮
    bf16: bool = field(default=True)
    tf32: bool = field(default=True)

    # ╭─────────────────── Training Schedule ─────────────────╮
    num_train_epochs: int = field(default=3)
    per_device_train_batch_size: int = field(default=1)
    gradient_accumulation_steps: int = field(default=16)
    learning_rate: float = field(default=1e-5)
    max_grad_norm: float = field(default=0.3)
    lr_scheduler_type: str = field(default="cosine")
    warmup_ratio: float = field(default=0.03)
    weight_decay: float = field(default=0.0)

    # ╭─────────────────── Data Processing ───────────────────╮
    remove_unused_columns: bool = field(default=False)  # Fix for custom dataset columns


# ╭──────────────────── tokeniser/embedding utils ──────────────────╮
def smart_tokenizer_and_embedding_resize(
    special_tokens_dict: Dict[str, str],
    tokenizer: transformers.PreTrainedTokenizer,
    model: transformers.PreTrainedModel,
):
    added = tokenizer.add_special_tokens(special_tokens_dict)
    model.resize_token_embeddings(len(tokenizer))
    if added:
        w_i = model.get_input_embeddings().weight.data
        w_o = model.get_output_embeddings().weight.data
        w_i_avg = w_i[:-added].mean(dim=0, keepdim=True)
        w_o_avg = w_o[:-added].mean(dim=0, keepdim=True)
        w_i[-added:] = w_i_avg
        w_o[-added:] = w_o_avg


# OLD - For sentence boundary splitting
# # ╭─────────────────────── depth-slicing helpers ───────────────────╮
# _SENT_SPLIT = re.compile(r"(?<=[.!?])\s+")


# def split_steps(text: str) -> List[str]:
#     "Rough sentence splitter."
#     return [s.strip() for s in _SENT_SPLIT.split(text) if s.strip()]


# def depth_prefixes(reasoning: str, depths: Tuple[int, ...]) -> Dict[int, str]:
#     """
#     Return {depth: truncated_text}. depth==0 → answer-only (last sentence).
#     """
#     steps = split_steps(reasoning)
#     prefixes = {}
#     for d in depths:
#         if d == 0:
#             prefixes[d] = steps[-1]  # answer only
#         else:
#             body = " ".join(steps[:d])
#             prefixes[d] = (body + " " + steps[-1]).strip()
#     return prefixes


# NEW - For length based depth slicing
# ╭─────────────────────── depth-slicing helpers ───────────────────╮
def find_answer_start(
    token_ids: torch.Tensor, boxed_token_id: int, fallback: int = 6
) -> int:
    """
    Scan from the end of `token_ids` until the first occurrence of the
    sentinel `boxed_token_id` (e.g. the token for '\\boxed').
    Returns that index; if the sentinel is not found, falls back to
    `len(token_ids) - fallback`.
    """
    for idx in range(len(token_ids) - 1, -1, -1):
        if token_ids[idx] == boxed_token_id:
            return idx
    return max(len(token_ids) - fallback, 0)


def token_prefixes(
    reasoning_ids: torch.Tensor,
    depths: tuple[int, ...],
    boxed_token_id: int,
    answer_fallback: int = 6,
) -> dict[int, torch.Tensor]:
    """
    Build Matryoshka prefixes keyed by `depth`.  Depth 0 = answer only;
    depth d>0 = first `d` reasoning tokens + full answer.

    Parameters
    ----------
    reasoning_ids    : 1-D tensor containing the entire assistant string
                       (reasoning + answer, no BOS/EOS tokens).
    depths           : depths required this forward pass, e.g. (0,16,32).
    boxed_token_id   : tokenizer id that starts the boxed answer.
    answer_fallback  : how many tokens from the end to keep if the sentinel
                       cannot be found.
    """
    ans_start = find_answer_start(reasoning_ids, boxed_token_id, answer_fallback)
    answer_ids = reasoning_ids[ans_start:]  # includes \boxed … period
    think_ids = reasoning_ids[:ans_start]  # pure reasoning

    out = {}
    for d in depths:
        if d == 0:
            out[d] = answer_ids
        else:
            out[d] = torch.cat([think_ids[:d], answer_ids])
    return out


def calculate_depths_and_weights(
    data_args: DataArguments,
    dataset_solutions: List[str] = None,
    tokenizer: transformers.PreTrainedTokenizer = None,
) -> Tuple[Tuple[int, ...], Tuple[float, ...]]:
    """
    Calculate depths and weights based on configuration.

    Args:
        data_args: DataArguments containing depth configuration
        dataset_solutions: Optional list of solution texts to analyze for token-based depths
        tokenizer: Required for token-based depth calculation

    Returns:
        Tuple of (depths, weights)
    """
    # Handle custom depths first
    if data_args.custom_depths:
        try:
            depths = tuple(int(d.strip()) for d in data_args.custom_depths.split(","))
            weights = tuple(1.0 for _ in depths)
            logging.warning(f"Using custom depths: {depths}")
            return depths, weights
        except ValueError as e:
            logging.warning(
                f"Invalid custom_depths format '{data_args.custom_depths}': {e}. Falling back to default."
            )

    # Token-based depths (new approach)
    if not data_args.use_sentence_depths:
        if dataset_solutions is None or tokenizer is None:
            logging.warning(
                "No dataset solutions or tokenizer provided for token-based depths. Using sentence-based fallback."
            )
            # Fallback to sentence-based
            data_args_fallback = DataArguments(
                num_depths=data_args.num_depths, use_sentence_depths=True
            )
            return calculate_depths_and_weights(data_args_fallback, None, None)

        # Analyze token lengths of reasoning parts
        reasoning_lengths = []
        boxed_token_id = (
            tokenizer.encode("\\boxed", add_special_tokens=False)[0]
            if tokenizer.encode("\\boxed", add_special_tokens=False)
            else None
        )

        for sol in dataset_solutions[:1000]:  # Sample first 1000 for efficiency
            try:
                sol_ids = tokenizer(
                    sol, add_special_tokens=False, return_tensors="pt"
                ).input_ids[0]
                if boxed_token_id is not None:
                    ans_start = find_answer_start(sol_ids, boxed_token_id, 6)
                    reasoning_length = ans_start
                else:
                    # Fallback: assume last 20% is answer
                    reasoning_length = int(len(sol_ids) * 0.8)
                reasoning_lengths.append(reasoning_length)
            except Exception:
                continue

        if not reasoning_lengths:
            logging.warning(
                "No valid reasoning lengths found. Using sentence-based fallback."
            )
            data_args_fallback = DataArguments(
                num_depths=data_args.num_depths, use_sentence_depths=True
            )
            return calculate_depths_and_weights(data_args_fallback, None, None)

        max_reasoning_length = max(reasoning_lengths)
        avg_reasoning_length = sum(reasoning_lengths) / len(reasoning_lengths)

        logging.warning(
            f"Reasoning token analysis: max_length={max_reasoning_length}, avg_length={avg_reasoning_length:.1f}"
        )

        # Generate token-based depths
        if data_args.num_depths == 1:
            depths = (0,)
        else:
            # Always include 0 (answer-only), then divide reasoning tokens evenly
            step = (
                max_reasoning_length // (data_args.num_depths - 1)
                if data_args.num_depths > 1
                else max_reasoning_length
            )
            depths = tuple(
                [0]
                + [
                    min(step * i, max_reasoning_length)
                    for i in range(1, data_args.num_depths)
                ]
            )

        weights = tuple(1.0 for _ in depths)
        logging.warning(f"Using token-based depths: {depths}")
        return depths, weights

    # Sentence-based depths (original behavior)
    if data_args.num_depths == 4:
        depths = (0, 2, 4, 8)  # Original default
    else:
        # Generate sentence-based depths: 0, then evenly spaced up to 2*num_depths
        max_sentences = 2 * data_args.num_depths
        if data_args.num_depths == 1:
            depths = (0,)
        else:
            step = max_sentences // (data_args.num_depths - 1)
            depths = tuple([0] + [step * i for i in range(1, data_args.num_depths)])

    weights = tuple(1.0 for _ in depths)
    logging.warning(f"Using sentence-based depths: {depths}")
    return depths, weights


# ╭──────────────────────── dataset classes ────────────────────────╮
class MatryoshkaDataset(Dataset):
    """
    • prompt_ids  : tokenised system + user
    • solution_txt: raw assistant reasoning + answer
    """

    def __init__(
        self, path: str, tokenizer: transformers.PreTrainedTokenizer, max_length: int
    ):
        super().__init__()
        logging.warning("Loading dataset…")
        if path.endswith(".jsonl"):
            rows = [json.loads(l) for l in open(path, "r", encoding="utf-8")]
        else:  # HF dataset directory
            rows = load_from_disk(path)

        sys_prompt = "You are a helpful assistant."
        self.prompt_ids, self.solution_txt = [], []

        for r in rows:
            user_msg = r.get("user") or r.get("question")
            asst_msg = r.get("assistant") or r.get("solution")
            if not (user_msg and asst_msg):
                continue  # skip bad rows

            prompt = [
                {"role": "system", "content": sys_prompt},
                {"role": "user", "content": user_msg.strip()},
            ]
            ids = tokenizer.apply_chat_template(prompt, return_tensors="pt")[0]
            # truncate very long prompts
            if len(ids) > max_length:
                ids = ids[-max_length:]
            self.prompt_ids.append(ids)
            self.solution_txt.append(asst_msg.strip())

        logging.warning(f"Prepared {len(self.prompt_ids):,} samples.")

    def __len__(self):
        return len(self.prompt_ids)

    def __getitem__(self, idx):
        return {
            "prompt_ids": self.prompt_ids[idx],
            "solution_txt": self.solution_txt[idx],
        }


@dataclass
class DataCollatorMatryoshka:
    tokenizer: transformers.PreTrainedTokenizer

    def __call__(self, batch):
        prompt_ids = [b["prompt_ids"] for b in batch]
        prompt_ids = torch.nn.utils.rnn.pad_sequence(
            prompt_ids,
            batch_first=True,
            padding_value=self.tokenizer.pad_token_id,
        )
        return {
            "prompt_ids": prompt_ids,
            "solution_txt": [b["solution_txt"] for b in batch],
            "attention_mask": prompt_ids.ne(self.tokenizer.pad_token_id),
        }


# ╭────────────────────── custom Trainer class ────────────────────╮
class MatryoshkaTrainer(Trainer):
    def __init__(self, depths, weights, **kw):
        super().__init__(**kw)
        self.depths = tuple(depths)
        self.weights = torch.tensor(weights, dtype=torch.float32)
        assert len(self.depths) == len(self.weights)

    # OLD - For sentence boundary splitting
    # def compute_loss(
    #     self, model, inputs, return_outputs=False, num_items_in_batch=None
    # ):
    #     prompt_ids = inputs["prompt_ids"]  # (B, P)
    #     attn_prompt = inputs["attention_mask"]
    #     solutions = inputs["solution_txt"]  # list[str]
    #     B = prompt_ids.size(0)
    #     device = prompt_ids.device

    #     total_loss = 0.0
    #     for depth, w in zip(self.depths, self.weights.to(device)):
    #         seqs, labels = [], []
    #         for i in range(B):
    #             prefix = depth_prefixes(solutions[i], (depth,))[depth]
    #             tgt_ids = (
    #                 self.tokenizer(
    #                     prefix,
    #                     add_special_tokens=False,
    #                     return_tensors="pt",
    #                 )
    #                 .input_ids[0]
    #                 .to(device)
    #             )
    #             seq = torch.cat([prompt_ids[i], tgt_ids])
    #             lab = seq.clone()
    #             lab[: len(prompt_ids[i])] = IGNORE_INDEX
    #             seqs.append(seq)
    #             labels.append(lab)

    #         seqs = torch.nn.utils.rnn.pad_sequence(
    #             seqs, batch_first=True, padding_value=self.tokenizer.pad_token_id
    #         )
    #         labels = torch.nn.utils.rnn.pad_sequence(
    #             labels, batch_first=True, padding_value=IGNORE_INDEX
    #         )
    #         attn = seqs.ne(self.tokenizer.pad_token_id)

    #         out = model(input_ids=seqs, attention_mask=attn, labels=labels)
    #         total_loss = total_loss + w * out.loss

    #     total_loss = total_loss / self.weights.sum()
    #     return (total_loss, None) if return_outputs else total_loss

    # NEW - For length based depth slicing
    def compute_loss(
        self, model, inputs, return_outputs: bool = False, num_items_in_batch=None
    ):

        prompt_ids = inputs["prompt_ids"]  # (B, P)
        solutions = inputs["solution_txt"]  # list[str]
        device = prompt_ids.device
        B = prompt_ids.size(0)

        total_loss = 0.0
        boxed_id = self.tokenizer.convert_tokens_to_ids("\\boxed")  # sentinel

        for depth, weight in zip(self.depths, self.weights.to(device)):
            seqs, lbls = [], []

            for i in range(B):
                # tokenise once per sample
                full_ids = (
                    self.tokenizer(
                        solutions[i], add_special_tokens=False, return_tensors="pt"
                    )
                    .input_ids[0]
                    .to(device)
                )

                # build the truncated assistant prefix for this depth
                tgt_ids = token_prefixes(full_ids, (depth,), boxed_token_id=boxed_id)[
                    depth
                ]

                # concatenate prompt ⊕ assistant-prefix
                seq = torch.cat([prompt_ids[i], tgt_ids])
                lab = seq.clone()
                lab[: len(prompt_ids[i])] = IGNORE_INDEX  # mask the prompt

                seqs.append(seq)
                lbls.append(lab)

            # pad to equal length inside this depth micro-batch
            seqs = torch.nn.utils.rnn.pad_sequence(
                seqs, batch_first=True, padding_value=self.tokenizer.pad_token_id
            )
            lbls = torch.nn.utils.rnn.pad_sequence(
                lbls, batch_first=True, padding_value=IGNORE_INDEX
            )
            attn = seqs.ne(self.tokenizer.pad_token_id)

            outputs = model(input_ids=seqs, attention_mask=attn, labels=lbls)
            total_loss = total_loss + weight * outputs.loss

        total_loss = total_loss / self.weights.sum()
        return (total_loss, None) if return_outputs else total_loss


# ╭──────────────────────────── training ───────────────────────────╮
def make_data(tokenizer, data_args):
    ds = MatryoshkaDataset(data_args.data_name, tokenizer, data_args.max_length)
    collator = DataCollatorMatryoshka(tokenizer)

    # Calculate depths and weights dynamically
    depths, weights = calculate_depths_and_weights(
        data_args,
        dataset_solutions=ds.solution_txt,  # Pass the solution texts for analysis
        tokenizer=tokenizer,
    )

    return dict(
        train_dataset=ds,
        eval_dataset=None,
        data_collator=collator,
        depths=depths,
        weights=weights,
    )


def train(model_args, data_args, training_args):
    setproctitle(training_args.proctitle)

    # hard-coded local model paths (adjust to your env)
    path_map = {
        "Qwen3-8B": "/data_x/junkim100/models/Qwen3-8B",
        "Llama-3.1-8B-Instruct": "/data_x/junkim100/models/Llama-3.1-8B-Instruct",
    }
    model_path = path_map.get(
        model_args.model_name_or_path, model_args.model_name_or_path
    )

    model = transformers.AutoModelForCausalLM.from_pretrained(
        model_path, torch_dtype="auto"
    )
    tokenizer = transformers.AutoTokenizer.from_pretrained(
        model_path,
        model_max_length=data_args.max_length,
        padding_side="right",
        use_fast=False,
        cache_dir=training_args.cache_dir,
    )

    if tokenizer.pad_token is None:
        smart_tokenizer_and_embedding_resize(
            {"pad_token": DEFAULT_PAD_TOKEN}, tokenizer, model
        )

    # build data + trainer
    dm = make_data(tokenizer, data_args)

    # Extract depths and weights from data creation
    depths = dm.pop("depths")
    weights = dm.pop("weights")

    trainer = MatryoshkaTrainer(
        model=model,
        tokenizer=tokenizer,
        args=training_args,
        depths=depths,
        weights=weights,
        **dm,
    )

    trainer.train()
    trainer.save_state()
    trainer.save_model(training_args.output_dir)


# ╭───────────────────────────── main ──────────────────────────────╮
if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s | %(levelname)s | %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    parser = transformers.HfArgumentParser(
        (ModelArguments, DataArguments, TrainingArguments)
    )
    m_args, d_args, t_args = parser.parse_args_into_dataclasses()

    # ╭─────────────────── Configure Run Name First ─────────────────╮
    # Set run_name to wb_name if not explicitly provided via command line
    if t_args.run_name is None:
        t_args.run_name = t_args.wb_name

    # ╭─────────────────── Configure Paths & Names ──────────────────╮
    # Only override output_dir if not explicitly provided via command line
    if t_args.output_dir == "./output":  # Default value from TrainingArguments
        t_args.output_dir = os.path.join(
            "./output", m_args.model_name_or_path, m_args.output_name
        )
    os.makedirs(t_args.output_dir, exist_ok=True)

    # ╭─────────────────── Configure Wandb ───────────────────────────╮
    os.environ["WANDB_PROJECT"] = t_args.wb_project
    os.environ["WANDB_LOG_MODEL"] = t_args.wb_name

    train(m_args, d_args, t_args)
    torch.cuda.empty_cache()
