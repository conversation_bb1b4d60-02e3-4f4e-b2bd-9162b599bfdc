"""
train_matryoshka_cot.py
—————————————————————————————————————————————————————————
End-to-end fine-tuning script for Matryoshka-style Chain-of-Thought
training on decoder-only LLMs.

• Reads a JSONL file where each line is
  { "user": "...", "assistant": "reasoning … final answer" }.

• Applies on-the-fly depth slicing (0, 2, 4, 8 sentences by default)
  and computes a weighted sum of XE losses so the model can
  “exit early” for easy problems.

—————————————————————————————————————————————————————————
"""

import os, json, re, copy, logging
from dataclasses import dataclass, field
from typing import Dict, List, Sequence, Optional, Tuple

import numpy as np
import torch
from torch.utils.data import Dataset
import transformers
from transformers import Trainer
from datasets import load_from_disk
from setproctitle import setproctitle

# ╭─────────────────────────── constants ───────────────────────────╮
IGNORE_INDEX       = -100
DEFAULT_PAD_TOKEN  = "[PAD]"
DEFAULT_EOS_TOKEN  = "</s>"
DEFAULT_BOS_TOKEN  = "</s>"
DEFAULT_UNK_TOKEN  = "</s>"
DEPTHS             = (0, 2, 4, 8)        # reasoning depths
DEPTH_WEIGHTS      = (1.0, 1.0, 1.0, 1.0)

# ╭──────────────────────── dataclass args ─────────────────────────╮
@dataclass
class ModelArguments:
    model_name_or_path: str   = field(default="Llama-3.1-8B-Instruct")
    output_name: str          = field(default="matryoshka_cot")

@dataclass
class DataArguments:
    data_name: str            = field(
        default="combined_cot_openmath.jsonl",
        metadata={"help": "JSONL file or HF dataset path"}
    )
    max_length: int           = field(
        default=4096,
        metadata={"help": "Maximum total sequence length"}
    )

@dataclass
class TrainingArguments(transformers.TrainingArguments):
    cache_dir: Optional[str]  = field(default=None)
    optim: str                = field(
        default="adamw_torch",
        metadata={"help": "adamw_torch, paged_adamw_32bit, adamw_bnb_8bit"}
    )
    report_to: str            = field(default="wandb")
    wb_name: str              = field(default="matryoshka-run")
    wb_project: str           = field(default="matryoshka-cot")
    proctitle: str            = field(default="matryoshka-cot")

    bf16: bool                = field(default=True)
    tf32: bool                = field(default=True)

    num_train_epochs: int     = field(default=3)
    per_device_train_batch_size: int = field(default=1)
    gradient_accumulation_steps: int = field(default=16)

    logging_steps: int        = field(default=10)
    learning_rate: float      = field(default=1e-5)
    max_grad_norm: float      = field(default=0.3)
    lr_scheduler_type: str    = field(default="cosine")
    warmup_ratio: float       = field(default=0.03)
    weight_decay: float       = field(default=0.0)

    deepspeed: str            = field(default=None)

# ╭──────────────────── tokeniser/embedding utils ──────────────────╮
def smart_tokenizer_and_embedding_resize(
    special_tokens_dict: Dict[str, str],
    tokenizer: transformers.PreTrainedTokenizer,
    model: transformers.PreTrainedModel,
):
    added = tokenizer.add_special_tokens(special_tokens_dict)
    model.resize_token_embeddings(len(tokenizer))
    if added:
        w_i = model.get_input_embeddings().weight.data
        w_o = model.get_output_embeddings().weight.data
        w_i_avg = w_i[:-added].mean(dim=0, keepdim=True)
        w_o_avg = w_o[:-added].mean(dim=0, keepdim=True)
        w_i[-added:] = w_i_avg
        w_o[-added:] = w_o_avg

# ╭─────────────────────── depth-slicing helpers ───────────────────╮
_SENT_SPLIT = re.compile(r"(?<=[.!?])\s+")

def split_steps(text: str) -> List[str]:
    "Rough sentence splitter."
    return [s.strip() for s in _SENT_SPLIT.split(text) if s.strip()]

def depth_prefixes(reasoning: str,
                   depths: Tuple[int, ...]) -> Dict[int, str]:
    """
    Return {depth: truncated_text}. depth==0 → answer-only (last sentence).
    """
    steps = split_steps(reasoning)
    prefixes = {}
    for d in depths:
        if d == 0:
            prefixes[d] = steps[-1]                    # answer only
        else:
            body = " ".join(steps[:d])
            prefixes[d] = (body + " " + steps[-1]).strip()
    return prefixes

# ╭──────────────────────── dataset classes ────────────────────────╮
class MatryoshkaDataset(Dataset):
    """
    • prompt_ids  : tokenised system + user
    • solution_txt: raw assistant reasoning + answer
    """
    def __init__(self, path: str,
                 tokenizer: transformers.PreTrainedTokenizer,
                 max_length: int):
        super().__init__()
        logging.warning("Loading dataset…")
        if path.endswith(".jsonl"):
            rows = [json.loads(l) for l in open(path, "r", encoding="utf-8")]
        else:                                      # HF dataset directory
            rows = load_from_disk(path)

        sys_prompt = "You are a helpful assistant."
        self.prompt_ids, self.solution_txt = [], []

        for r in rows:
            user_msg  = r.get("user")      or r.get("question")
            asst_msg  = r.get("assistant") or r.get("solution")
            if not (user_msg and asst_msg):
                continue                                      # skip bad rows

            prompt = [
                {"role": "system", "content": sys_prompt},
                {"role": "user",   "content": user_msg.strip()},
            ]
            ids = tokenizer.apply_chat_template(
                prompt, return_tensors="pt"
            )[0]
            # truncate very long prompts
            if len(ids) > max_length:
                ids = ids[-max_length:]
            self.prompt_ids.append(ids)
            self.solution_txt.append(asst_msg.strip())

        logging.warning(f"Prepared {len(self.prompt_ids):,} samples.")

    def __len__(self):
        return len(self.prompt_ids)

    def __getitem__(self, idx):
        return {
            "prompt_ids":   self.prompt_ids[idx],
            "solution_txt": self.solution_txt[idx],
        }

@dataclass
class DataCollatorMatryoshka:
    tokenizer: transformers.PreTrainedTokenizer

    def __call__(self, batch):
        prompt_ids = [b["prompt_ids"] for b in batch]
        prompt_ids = torch.nn.utils.rnn.pad_sequence(
            prompt_ids, batch_first=True,
            padding_value=self.tokenizer.pad_token_id,
        )
        return {
            "prompt_ids":   prompt_ids,
            "solution_txt": [b["solution_txt"] for b in batch],
            "attention_mask": prompt_ids.ne(self.tokenizer.pad_token_id),
        }

# ╭────────────────────── custom Trainer class ────────────────────╮
class MatryoshkaTrainer(Trainer):
    def __init__(self, depths=DEPTHS, weights=DEPTH_WEIGHTS, **kw):
        super().__init__(**kw)
        self.depths   = tuple(depths)
        self.weights  = torch.tensor(weights, dtype=torch.float32)
        assert len(self.depths) == len(self.weights)

    def compute_loss(self, model, inputs, return_outputs=False):
        prompt_ids     = inputs["prompt_ids"]        # (B, P)
        attn_prompt    = inputs["attention_mask"]
        solutions      = inputs["solution_txt"]      # list[str]
        B              = prompt_ids.size(0)
        device         = prompt_ids.device

        total_loss = 0.0
        for depth, w in zip(self.depths, self.weights.to(device)):
            seqs, labels = [], []
            for i in range(B):
                prefix = depth_prefixes(solutions[i], (depth,))[depth]
                tgt_ids = self.tokenizer(
                    prefix,
                    add_special_tokens=False,
                    return_tensors="pt",
                ).input_ids[0]
                seq = torch.cat([prompt_ids[i], tgt_ids]).to(device)
                lab = seq.clone()
                lab[: len(prompt_ids[i])] = IGNORE_INDEX
                seqs.append(seq)
                labels.append(lab)

            seqs   = torch.nn.utils.rnn.pad_sequence(
                        seqs, batch_first=True,
                        padding_value=self.tokenizer.pad_token_id
                    )
            labels = torch.nn.utils.rnn.pad_sequence(
                        labels, batch_first=True,
                        padding_value=IGNORE_INDEX
                    )
            attn = seqs.ne(self.tokenizer.pad_token_id)

            out = model(input_ids=seqs, attention_mask=attn, labels=labels)
            total_loss = total_loss + w * out.loss

        total_loss = total_loss / self.weights.sum()
        return (total_loss, None) if return_outputs else total_loss

# ╭──────────────────────────── training ───────────────────────────╮
def make_data(tokenizer, data_args):
    ds = MatryoshkaDataset(data_args.data_name, tokenizer, data_args.max_length)
    collator = DataCollatorMatryoshka(tokenizer)
    return dict(train_dataset=ds, eval_dataset=None, data_collator=collator)

def train(model_args, data_args, training_args):
    setproctitle(training_args.proctitle)

    # hard-coded local model paths (adjust to your env)
    path_map = {
        "Qwen3-8B": "/data_x/junkim100/models/Qwen3-8B",
        "Llama-3.1-8B-Instruct": "/data_x/junkim100/models/Llama-3.1-8B-Instruct",
    }
    model_path = path_map.get(model_args.model_name_or_path,
                              model_args.model_name_or_path)

    model = transformers.AutoModelForCausalLM.from_pretrained(
        model_path,
        torch_dtype="auto"
    )
    tokenizer = transformers.AutoTokenizer.from_pretrained(
        model_path,
        model_max_length=data_args.max_length,
        padding_side="right",
        use_fast=False,
        cache_dir=training_args.cache_dir,
    )

    if tokenizer.pad_token is None:
        smart_tokenizer_and_embedding_resize(
            {"pad_token": DEFAULT_PAD_TOKEN},
            tokenizer, model
        )

    # build data + trainer
    dm = make_data(tokenizer, data_args)

    trainer = MatryoshkaTrainer(
        model               = model,
        tokenizer           = tokenizer,
        args                = training_args,
        depths              = DEPTHS,
        weights             = DEPTH_WEIGHTS,
        **dm,
    )

    trainer.train()
    trainer.save_state()
    trainer.save_model(training_args.output_dir)

# ╭───────────────────────────── main ──────────────────────────────╮
if __name__ == "__main__":
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s | %(levelname)s | %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    parser = transformers.HfArgumentParser(
        (ModelArguments, DataArguments, TrainingArguments)
    )
    m_args, d_args, t_args = parser.parse_args_into_dataclasses()

    t_args.output_dir = os.path.join(
        "./output", m_args.model_name_or_path, m_args.output_name
    )
    os.makedirs(t_args.output_dir, exist_ok=True)

    os.environ["WANDB_PROJECT"]  = t_args.wb_project
    os.environ["WANDB_LOG_MODEL"] = t_args.wb_name

    train(m_args, d_args, t_args)
    torch.cuda.empty_cache()
